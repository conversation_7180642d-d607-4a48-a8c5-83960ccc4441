//GLOBAL STATUS
exports.STATUS_CODES = {
    // 1XX INFORMATIONAL
    CONTINUE: 100,
    SWITCHING_PROTOCOLS: 101,
    PROCESSING: 102,
    EARLY_HINTS: 103,

    // 2XX SUCCESS
    SUCCESS: 200,
    CREATED: 201,
    ACCEPTED: 202,
    NON_AUTHORITATIVE_INFORMATION: 203,
    NO_CONTENT: 204,
    RESET_CONTENT: 205,
    PARTIAL_CONTENT: 206,
    MULTI_STATUS: 207,
    ALREADY_REPORTED: 208,
    IM_USED: 226,

    // 3XX REDIRECTION
    MULTIPLE_CHOICES: 300,
    MOVED_PERMANENTLY: 301,
    FOUND: 302,
    SEE_OTHER: 303,
    NOT_MODIFIED: 304,
    USE_PROXY: 305,
    TEMPORARY_REDIRECT: 307,
    PERMANENT_REDIRECT: 308,

    // 4XX CLIENT ERROR
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    PAYMENT_REQUIRED: 402,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    NOT_ALLOWED: 405,
    NOT_ACCEPTABLE: 406,
    PROXY_AUTHENTICATION_REQUIRED: 407,
    REQUEST_TIMEOUT: 408,
    CONFLICT: 409,
    GONE: 410,
    LENGTH_REQUIRED: 411,
    PRECONDITION_FAILED: 412,
    PAYLOAD_TOO_LARGE: 413,
    URI_TOO_LONG: 414,
    UNSUPPORTED_MEDIA_TYPE: 415,
    RANGE_NOT_SATISFIABLE: 416,
    EXPECTATION_FAILED: 417,
    UNPROCESSABLE_ENTITY: 422,
    VALIDATION_ERROR: 422,
    NOT_VALID_DATA: 422,
    LOCKED: 423,
    FAILED_DEPENDENCY: 424,
    UNORDERED_COLLECTION: 425,
    UPGRADE_REQUIRED: 426,
    PRECONDITION_REQUIRED: 428,
    TOO_MANY_REQUESTS: 429,
    REQUEST_HEADER_FIELDS_TOO_LARGE: 431,
    UNAVAILABLE_FOR_LEGAL_REASONS: 451,

    // 5XX SERVER ERROR
    SERVER_ERROR: 500,
    NOT_IMPLEMENTED: 501,
    BAD_GATEWAY: 502,
    SERVICE_UNAVAILABLE: 503,
    GATEWAY_TIMEOUT: 504,
    HTTP_VERSION_NOT_SUPPORTED: 505,
    VARIANT_ALSO_NEGOTIATES: 506,
    INSUFFICIENT_STORAGE: 507,
    LOOP_DETECTED: 508,
    BANDWIDTH_LIMIT_EXCEEDED: 509,
    NOT_EXTENDED: 510,
    NETWORK_AUTHENTICATION_REQUIRED: 511,

    // MongoDB duplicate key code
    MONGODB_DUPLICATE_KEY_CODE: 11000,
}

exports.ENVIRONMENTS = {
    DEVELOPMENT: "development",
    STAGING: "staging",
    PRODUCTION: "production"
}

exports.BUCKET_TYPE = {
    "PRIVATE": "PRIVATE",
    "PUBLIC": "PUBLIC",
    "LOCALES": "LOCALES"
}

exports.BOOLEAN = {
    TRUE: "TRUE",
    FALSE: "FALSE",
}

exports.GENDER = {
    MALE: "male",
    FEMALE: "female"
}

exports.SIGNING_TYPE = {
    MOBILE: "MOBILE",
    EMAIL: "EMAIL"
}

exports.MONGODB = {
    OPERATION_TYPE: {
        INSERT: "insert",
        UPDATE: "update",
    }
}

exports.REGEX = {
    COUNTRY_CODE: /^\+\d+$/,
    EMAIL_VALID: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/
}
exports.LISTING_TYPES = {
    ALL: "ALL",
    SEARCH: "SEARCH",
    PAGINATION: "PAGINATION"
}
exports.SMS_PROVIDER_INFO = {
    UNIFONIC: "UNIFONIC"
}

exports.ADD_TENANT_SCREENS = {
    DETAILS: "DETAILS",
    SERVICES: "SERVICES",
    BRANCHES: "BRANCHES",
    ADVANCED: "ADVANCED"
}

exports.TENANT_SERVICES = {
    QUOTATION: "quotations",
    DEALS: "deals",
    COLLECTION: "collections",
    CUSTOMER_APP: "customer_app",
    PUSH_NOTIFICATION: "push_notifications"
}

// exports.SMS_SERVICE_PROVIDERS = {
//     UNIFONIC: "UNIFONIC"
// }

exports.ENTITY_STATUS = {
    ACTIVE: "ACTIVE",
    INACTIVE: "INACTIVE",
    ALL: "ALL"
}

exports.QR_CODE_TYPE = {
    REWARD_PROGRAM_MEMBER: "rewardProgramMember",
}

exports.TIME_UNITS = {
    seconds: "seconds",
    minutes: "minutes",
    hours: "hours",
    days: "days"
};

exports.CUSTOMER_TYPE = {
    ALL: "ALL",
    NEW: "NEW",
    UNASSIGNED: "UNASSIGNED",
    INACTIVE: "INACTIVE",
    APP_ACCESS: "APP_ACCESS",
    ACTIVE: "ACTIVE",
    APP_REQUEST: "APP_REQUEST"
}

exports.CUSTOMER_PAYMENT_FILTER_TYPE = {
    ALL: "ALL",
    PENDING: "PENDING",
    PAYMENT_ENABLED: "PAYMENT_ENABLED",
    WITHOUT_PAGINATION: "WITHOUT_PAGINATION"
}

exports.CUSTOMER_REWARD_PROGRAM_FILTER_TYPE = {
    ALL: "ALL",
    PENDING: "PENDING",
    ENROLLED: "ENROLLED",
}

exports.CUSTOMER_STATUS_TYPE = {
    CUSTOMER_APP_ACCESS: "CUSTOMER_APP_ACCESS",
    STATUS: "STATUS",
    APP_ACCESS: "APP_ACCESS",
    ONLY_APP_ACCESS: "ONLY_APP_ACCESS",
    ONLY_CATALOG_ACCESS: "ONLY_CATALOG_ACCESS"
};

exports.BRANCH_LIST_TYPE = {
    WAREHOUSE: "WAREHOUSE"
}

exports.DATA_SHEET = {
    DATA_TYPE: {
        PRODUCT: "PRODUCT",
        PRICE: "PRICE",
        INVENTORY: "INVENTORY",
        CUSTOMER: "CUSTOMER"
    },

    OPERATION_TYPE: {
        CREATE: "CREATE",
        UPDATE: "UPDATE",
        CUSTOM_FIELDS: "CUSTOM_FIELDS"
    },

    STATUS: {
        COMPLETE: "COMPLETE",
        FOR_REVIEW: "FOR_REVIEW",
        CANCELED: "CANCELED",
        IN_PROGRESS: "IN_PROGRESS"
    },

    SHEET_STATUS: {
        PENDING: "PENDING",
        COMPLETE: "COMPLETE"
    },

    APPROVE_TYPE: {
        ALL: "ALL",
        SELECTED: "SELECTED"
    },

    TYPE: {
        IMPORT: "IMPORT",
        EXPORT: "EXPORT",
    },

    UPDATE_TYPE: {
        DETAILS: "DETAILS",
        STATUS: "STATUS",
    },
};

exports.deviceTypeDetected = {
    "APPLICATION": "APPLICATION",
    "PORTAL": "PORTAL"
}

exports.TENANT_ADVANCE_LIMIT_KEYS = {
    'NUMBER_OF_USERS': 'NUMBER_OF_USERS',
    'NUMBER_OF_CUSTOMERS': 'NUMBER_OF_CUSTOMERS',
    'NUMBER_OF_PRODUCTS': 'NUMBER_OF_PRODUCTS',
    'STORAGE_ALLOWANCE': 'STORAGE_ALLOWANCE',
};

exports.TENANT_QUERY_TYPE_ENUM = {
    "QUERY_AND_PROJECTION": "QUERY_AND_PROJECTION"
}

exports.PROFILE_TYPE_ENUM = {
    "EXTRA_PROFILE": "EXTRA_PROFILE"
}

exports.LANGUAGE = {
    EN: "en",
    AR: "ar"
}

exports.VALUES = {
    DEVICE_TYPE: {
        ANDROID: "ANDROID",
        IOS: "IOS",
        WEB: "WEB",
        DESKTOP: "DESKTOP",
        TABLET: "TABLET"
    },

    deviceAccessType: {
        TABLET: "TABLET",
        MOBILE: "MOBILE"
    },

    deviceAccessOs: {
        IOS: "IOS",
        ANDROID: "ANDROID"
    },

    awsUserPoolRegion: process.env.AWS_POOL_REGION,
    awsPublicBucketBaseURL: process.env.AWS_PUBLIC_BUCKET_BASE_URL,
    userPoolData: {
        UserPoolId: process.env.AWS_USER_POOL_ID,
        ClientId: process.env.AWS_USER_POOL_CLIENT_ID,  //demouser-pool
        ClientSecret: process.env.AWS_USER_POOL_CLIENT_SECRET
    },
    internalServiceBaseURL: process.env.INTERNAL_SERVICE_BASE_URL,
    internalSalesPersonServiceBaseURL: process.env.INTERNAL_SALES_PERSON_SERVICE_BASE_URL,
    jsReportBaseURL: process.env.JS_REPORT_BASE_URL,
    jsReportUsername: process.env.JS_REPORT_USER_NAME,
    jsReportPassword: process.env.JS_REPORT_PASSWORD,
    portals: {
        SYSTEM_PORTAL: "SYSTEM_PORTAL",
        TENANT_PORTAL: "TENANT_PORTAL",
        BRANCH_PORTAL: "BRANCH_PORTAL",
        SALES_APP: "SALES_APP",
        CUSTOMER_APP: "CUSTOMER_APP",
        SUPERVISOR_APP: "SUPERVISOR_APP" // Requested by Hardik for adding this portal type for supervisor role
    },
    sessionStatus: {
        ACTIVE: "ACTIVE",
        CLOSE: "CLOSE",
        EXPIRED: "EXPIRED",
        LOGOUT: "LOGOUT",
    },
    MODULES: {
        SYSTEM_PORTAL: [
            "Dashboard",
            "Tenants",
            "System Users",
            "Settings",
            "Localization",
        ],
        TENANT_PORTAL: [
            "Orders",

        ]
    },
    PASSWORD_RESET_TYPE: {
        EMAIL_LINK: "EMAIL_LINK",
        MANUAL: "MANUAL"
    },
    PASSWORD_REST_OR_CREATE_DURATION: 60,
    CUSTOMER_SAMPLE_EXCEL_FILE: "https://hawak-locales.s3.me-south-1.amazonaws.com/dataSheet",

    // ENVIRONMENT check
    ENVIRONMENT: process.env.ENVIRONMENT,

    IS_DEV_ENV: process.env.ENVIRONMENT === this.ENVIRONMENTS.DEVELOPMENT,
    IS_STAGING_ENV: process.env.ENVIRONMENT === this.ENVIRONMENTS.STAGING,
    IS_PROD_ENV: process.env.ENVIRONMENT === this.ENVIRONMENTS.PRODUCTION,

    IS_APP_RUNNING_ON_SERVER: process.env.PM2_PROGRAMMATIC ? true : false,
}

exports.SALES_TYPE = {
    SALES_PERSON: "SALES_PERSON",
    ALL: "ALL"
}

exports.MEDIA_TYPE = {
    IMAGE: "IMAGE",
    VIDEO: "VIDEO",
};

exports.IMAGE_THUMB_SIZE = {
    HEIGHT: 750,
    WIDTH: 750
};

exports.EMAIL_VERIFIER_URL = process.env.BASE_URL + "auth/email-verification/"

exports.FILE_PATH = { // MEDIA PATH FOR AWS S3
    // USER MODULE
    USER_PROFILE: process.env.AWS_BUCKET_FOLDER + "user/profile",
    SYSTEM_USER_PROFILE: process.env.AWS_BUCKET_FOLDER + "user/system-user-profile",

    MASTER_LOCAL: process.env.AWS_LOCALES_FOLDER + "master",
    WEB_LOCAL: process.env.AWS_LOCALES_FOLDER + "web",
    TAB_LOCAL: process.env.AWS_LOCALES_FOLDER + "tab",

    SHIPPING_LABEL: process.env.AWS_BUCKET_FOLDER + "shipping-label",
    DATA_SHEET_CUSTOMER_EXPORT_SHEET: process.env.AWS_BUCKET_FOLDER + "dataSheet",
    VISIT_IMAGES: process.env.AWS_BUCKET_FOLDER + "visit-images",
    PAYMENT_TERMINAL_RECEIPT: process.env.AWS_BUCKET_FOLDER + "payments/terminal-receipts",
    CATEGORIES_FAMILY_IMAGES: process.env.AWS_BUCKET_FOLDER + "categories/family/original",

    STAND_ASSET_IMAGES: process.env.AWS_BUCKET_FOLDER + "stand/asset_images/original",
    STAND_IMAGES: process.env.AWS_BUCKET_FOLDER + "stand/images/original",

    SCHEDULED_NOTIFICATION_IMAGES: process.env.AWS_BUCKET_FOLDER + "notification/scheduled-images/original",
    REWARD_PROGRAM_PRODUCTS_IMAGES: process.env.AWS_BUCKET_FOLDER + "reward-product/original",
    PRODUCT_WEB_IMAGE: process.env.AWS_BUCKET_FOLDER + "product/web",
    FIREBASE_SERVICE_ACCOUNT: process.env.AWS_BUCKET_FOLDER + "firebase-service-account.json",

    S3_URL: {
        USER_PROFILE: this.VALUES.awsPublicBucketBaseURL + "user/profile",
        SYSTEM_USER_PROFILE: this.VALUES.awsPublicBucketBaseURL + "user/system-user-profile",
        SCHEDULED_NOTIFICATION_IMAGES: this.VALUES.awsPublicBucketBaseURL + "notification/scheduled-images/original",
        REWARD_PROGRAM_PRODUCTS_IMAGES: this.VALUES.awsPublicBucketBaseURL + "reward-product/original",

        COMPRESSED: {
            USER_PROFILE_THUMBNAIL: this.VALUES.awsPublicBucketBaseURL + "user/compressed-profile",
            SYSTEM_USER_PROFILE_THUMBNAIL: this.VALUES.awsPublicBucketBaseURL + "user/compressed-system-user-profile",
        }
    },
}

exports.PRIMITIVE_ROLES = {
    SYSTEM_OWNER: "System Owner",
    SUPER_ADMIN: "Super Admin",
    ACCOUNT_MANAGER: "Account Manager",
    TENANT_OWNER: "Tenant Owner",
    TENANT_ADMIN: "Admin",
    BRANCH_MANAGER: "Branch Manager",
    SALES_PERSON: "Sales Person",
    SUPERVISOR: "Supervisor",
    CUSTOMER: "Customer",
    WAREHOUSE_CLERK: "Warehouse Clerk",
    ACCOUNTANT: "Accountant",
    CONTRIBUTOR: "Contributor",
};

exports.ORDER_STATUS_TYPES = {
    PENDING: "PENDING",
    RECEIVED: "RECEIVED",
    RELEASED: "RELEASED",
    PREPARING: "PREPARING",
    SHIPPED: "SHIPPED",
    DELIVERED: "DELIVERED",
    CANCELLED: "CANCELLED",
    DASHBOARD_ORDERS: "DASHBOARD_ORDERS",
    ON_HOLD: "ON_HOLD"
}

exports.VERIFY_CUSTOMER_ACTIONS = {
    SEND_OTP: "SEND_OTP",
    VERIFY_OTP: "VERIFY_OTP",
}

exports.BY_PASS_OTP = 9815

exports.SEND_EMAIL_NUMBERS = [
    // Production users
    *********,

    // Testing users
    **********, **********
]

exports.APP_OTP_VALIDITY_IN_MINUTES = 10;

exports.CUSTOMER_ADDRESS_TYPE = {
    PORTAL: "PORTAL",
    SALES_APP: "SALES_APP",
    CUSTOMER_APP: "CUSTOMER_APP"
}

exports.TENANT_ALLOW_VALIDATION = { // Dec 30 users and customer limit validation
    CUSTOMER: "CUSTOMERS",
    USER: "USERS"
}

const JPG_EXT = ".jpg"
const JPEG_EXT = ".jpeg"
const PNG_EXT = ".png"
const SVG_EXT = ".svg"

exports.VALID_IMAGES_EXTS = [JPG_EXT, JPEG_EXT, PNG_EXT, SVG_EXT]

exports.OBJECT_UPLOAD_SIGNATURE_TYPE = {
    SHIPPING_LABEL: "SHIPPING_LABEL",
    VISIT_IMAGES: "VISIT_IMAGES",
    USER_PROFILE: "USER_PROFILE",
    SYSTEM_USER_PROFILE: "SYSTEM_USER_PROFILE",
    PAYMENT_TERMINAL_RECEIPT: "PAYMENT_TERMINAL_RECEIPT",
    STAND_ASSET_IMAGES: "STAND_ASSET_IMAGES",
    STAND_IMAGES: "STAND_IMAGES",
    SCHEDULED_NOTIFICATION_IMAGES: "SCHEDULED_NOTIFICATION_IMAGES",
    REWARD_PROGRAM_PRODUCTS_IMAGES: "REWARD_PROGRAM_PRODUCTS_IMAGES",
    CATEGORIES_FAMILY_IMAGES: "CATEGORIES_FAMILY_IMAGES",
}

exports.SIGNATURE_EXT_MAPPING = {
    [this.OBJECT_UPLOAD_SIGNATURE_TYPE.STAND_ASSET_IMAGES]: [JPG_EXT, JPEG_EXT, PNG_EXT],
    [this.OBJECT_UPLOAD_SIGNATURE_TYPE.CATEGORIES_FAMILY_IMAGES]: [JPEG_EXT, PNG_EXT]
}

exports.DURATION_PERIOD_OPTIONS = {
    DAY: "DAY",
    WEEK: "WEEK",
    MONTH: "MONTH"
}

exports.FALSELY_VALUES = [0, "", undefined, null, false, NaN, "0", "undefined", "null", "false", "NaN"]

exports.EMAIL_ID = {
    NOTIFICATION: "<EMAIL>",
    FARIS: "<EMAIL>",
    YOUSEF: "<EMAIL>",
    YASHASHWINI: "<EMAIL>",
    AALAP: "<EMAIL>",
    BRIJESH: "<EMAIL>",
    RAJAN: "<EMAIL>",
    DEEP: "<EMAIL>"
}

exports.HOLD_REASON_TEMPLATES = {
    OPTIONS_TYPE: {
        MAP: 'MAP',
        INPUT_FIELD: 'INPUT_FIELD'
    },
    OPTIONS: [
        {
            title: "Legal Name",
            type: "MAP",
            key: "legal_name"
        },
        {
            title: "Salesperson Name",
            type: "MAP",
            key: "sales_person_name"
        },
        {
            title: "Input Field",
            type: "INPUT_FIELD",
            key: "input_field"
        }
    ]
}

exports.MESSAGE_BIRD_API_DYNAMIC_ID = {
    WORKSPACE_ID: '{workspaceId}',
    PROJECT_ID: '{projectId}',
    TEMPLATE_ID: '{channelTemplateId}',
    CHANNEL_ID: '{channelId}'
};

exports.MESSAGE_BIRD = {
    API_END_POINT: {
        PROJECTS: `workspaces/${this.MESSAGE_BIRD_API_DYNAMIC_ID.WORKSPACE_ID}/projects`,
        PROJECTS_BY_ID: `workspaces/${this.MESSAGE_BIRD_API_DYNAMIC_ID.WORKSPACE_ID}/projects/${this.MESSAGE_BIRD_API_DYNAMIC_ID.PROJECT_ID}`,
        PROJECT_TEMPLATE_DETAILS: `workspaces/${this.MESSAGE_BIRD_API_DYNAMIC_ID.WORKSPACE_ID}/projects/${this.MESSAGE_BIRD_API_DYNAMIC_ID.PROJECT_ID}/channel-templates/${this.MESSAGE_BIRD_API_DYNAMIC_ID.TEMPLATE_ID}`,
        SEND_MESSAGE: `workspaces/${this.MESSAGE_BIRD_API_DYNAMIC_ID.WORKSPACE_ID}/channels/${this.MESSAGE_BIRD_API_DYNAMIC_ID.CHANNEL_ID}/messages`
    },
    PAGINATION_LIMIT: 40
}

exports.MESSAGE_REQUEST = {
    "sender": {
        "connector": {
            "identifierValue": 'string',
            "annotations": {
                "name": "string"
            }
        }
    },
    "receiver": {
        "contacts": [
            {
                "identifierKey": "string",
                "identifierValue": "string"
            }
        ]
    },
    "template": {
        "projectId": "",
        "version": "string",
        "locale": "string",
        "variables": {
        }
    }
}

exports.PERMISSION_MODULES = {
    CUSTOMERS: "customers",
    DASHBOARD: "dashboard",
    ORDERS: "orders",
    TRACKING: "tracking",
    PAYMENTS: "payments",
    PRODUCTS: "products",
    INVENTORY: "inventory",
    CATEGORIES: "categories",
    USERS: "users",
    IMAGES: "images",
    DEALS: "deals",
    COLLECTIONS: "collections",

    LANGUAGES: "languages",
    TENANTS: "tenants",
    REGIONS: "regions",
    CITIES: "cities",
    COUNTRIES: "countries",

    REWARD_PROGRAM: "reward_program",
    REWARD_PROGRAM_MEMBER: "reward_program_member",
    REWARD_PROGRAM_POINT: "reward_program_point",
    REWARD_PROGRAM_PRODUCT: "reward_program_product",
    REWARD_PROGRAM_PRODUCT_CLAIM: "reward_program_product_claim",

    DATA_SHEET: "datasheet",
    DATA_SHEET_PRODUCT: "datasheet_product",
    DATA_SHEET_PRICE: "datasheet_price",
    DATA_SHEET_INVENTORY: "datasheet_inventory",
    DATA_SHEET_CUSTOMER: "datasheet_customer",

    SETTINGS_MASTER_DATA: "settings_master_data",
    SETTINGS_MASTER_DATA_ATTRIBUTE: "settings_master_data_attribute",
    SETTINGS_MASTER_DATA_ATTRIBUTE_SET: "settings_master_data_attribute_set",
    SETTINGS_MASTER_DATA_PRICE_LIST: "settings_master_data_price_list",
    SETTINGS_MASTER_DATA_BRAND: "settings_master_data_brand",
    SETTINGS_MASTER_DATA_UOM: "settings_master_data_uom",

    SETTINGS_CONFIGURATIONS: "settings_configurations",
    SETTINGS_CONFIGURATIONS_ACCOUNT_INFO: "settings_configurations_account_info",
    SETTINGS_CONFIGURATIONS_SHIPPING_LABEL: "settings_configurations_shipping_label",
    SETTINGS_CONFIGURATIONS_APP_SETTING: "settings_configurations_app_setting",
    SETTINGS_CONFIGURATIONS_TAX_SETTING: "settings_configurations_tax_setting",
    SETTINGS_CONFIGURATIONS_TAX: "settings_configurations_tax",
    SETTINGS_CONFIGURATIONS_TRACKING: "settings_configurations_tracking",
    SETTINGS_CONFIGURATIONS_REWARD_PROGRAM: "settings_configurations_reward_program",

    SAP_SERVICE: "sap_service",

    SYSTEM_USERS: "system_users",
    SYSTEM_REGIONS: "system_regions",
    SYSTEM_CITIES: "system_cities",
    SYSTEM_COUNTRIES: "system_countries",
    SYSTEM_DASHBOARD: "system_dashboard",
    SYSTEM_SETTINGS_OTHER: "system_settings_other",
    SYSTEM_LANGUAGES: "system_languages",
    SYSTEM_LANGUAGE_KEYS: "system_language_keys",
    SYSTEM_TENANTS: "system_tenants",
    SYSTEM_ROLES: "system_roles",
}

exports.ACTIONS = {
    VIEW: "view",
    EDIT: "edit",
    DELETE: "delete",
    CREATE: "create",
};

exports.INTEGRATION_CHANNELS = {
    MESSAGE_BIRD: "MESSAGE_BIRD",
    SAP_SERVICE: "SAP_SERVICE",
}

exports.SETTINGS = {
    CONFIGURATIONS: {
        TYPE: {
            SHIPPING_LABEL: "SHIPPING_LABEL",
            APP_SETTING: "APP_SETTING",
        }
    }
}

exports.SAP_SERVICE = {
    DOCUMENT_TYPE: {
        INVOICE: "IN",
        PAYMENT: "RC",
        CREDIT_NOTES: "CN",
        JOURNAL_ENTRY: "JE"
    },
    CREDIT_MEMO_TYPE: {
        RETURNS: "Items",
        DISCOUNT: "Service"
    },
    ERROR: {
        API: "SAP_API_ERROR"
    }
}

exports.BULK_CREATE_HOLD_REASONS = [
    {
        is_whatsapp_message_enabled: false,
        is_active: true,
        tenant_id: undefined,
        icon: "OVERDUE_BALANCE",
        title: "Overdue balance",
        secondary_language_title: "Overdue balance",
        hold_templates: [],
        release_templates: [],
        created_by: undefined,
        updated_by: undefined,
        created_at: undefined,
        updated_at: undefined,
    },
    {
        is_whatsapp_message_enabled: false,
        is_active: true,
        tenant_id: undefined,
        title: "Awaiting Documents",
        icon: "AWAITING_DOCUMENT",
        secondary_language_title: "Awaiting Documents",
        hold_templates: [],
        release_templates: [],
        created_by: undefined,
        updated_by: undefined,
        created_at: undefined,
        updated_at: undefined,
    },
    {
        is_whatsapp_message_enabled: false,
        is_active: true,
        tenant_id: undefined,
        title: "Customer Requested",
        icon: "CUSTOMER_REQUESTED",
        secondary_language_title: "Customer Requested",
        hold_templates: [],
        release_templates: [],
        created_by: undefined,
        updated_by: undefined,
        created_at: undefined,
        updated_at: undefined,
    }
]

exports.TEMPLATE_REASON_TYPE = {
    HOLD_REASON: "hold_templates",
    RELEASE_REASON: "release_templates"
}

exports.REPORTS = {
    REPORT_TYPE: {
        ACCOUNT_STATEMENT: "ACCOUNT_STATEMENT",
    },
    TEMPLATE: {
        ACCOUNT_STATEMENT: "/statement/Main.html",
    },
}

exports.ERROR_MESSAGES = {
    SMS_SEND: "sms_could_not_be_sent",
}

exports.REWARD_PROGRAM = {
    MEMBER: {
        MEMBERSHIP_TYPE: {
            CLASSIC: "CLASSIC",
            VIP: "VIP",
        },
        COIN_RULES_CONFIGURATION_KEY: {
            //Keep key same as MEMBERSHIP_TYPE & value will dependent on reward program configuration schema
            CLASSIC: "classic_member_coin_rules",
            VIP: "vip_member_coin_rules",
        }
    },
    POINT: {
        TYPE: {
            COINS: "COINS",
            VIP_POINTS: "VIP_POINTS",
        },
        ENTRY_TYPE: {
            DISTRIBUTED: "DISTRIBUTED",
            EXPIRED: "EXPIRED",
            CLAIMED: "CLAIMED",
        },
        LOG_TYPE: {
            SAP: {
                INVOICE: {
                    PURCHASE: "PURCHASE_INVOICE",
                    CANCELLATION: "CANCELLATION_INVOICE",
                },
                PAYMENT: "PAYMENT",
                CREDIT_NOTES: {
                    RETURNS: "RETURNS_CREDIT_NOTES",
                    DISCOUNT: "DISCOUNT_CREDIT_NOTES",
                },
            },
            ACTION_TYPE: {
                DAILY_ACCESS: "DAILY_ACCESS",
                MEMBER_SCAN: "MEMBER_SCAN",
                MANUAL: "MANUAL",
            },
            SYSTEM: {
                MILESTONES: "MILESTONES",
            },
            VIP: {
                UPGRADE: "VIP_UPGRADE",
                RENEW: "VIP_RENEW",
            },
            EXPIRED: {
                COINS: "EXPIRED_COINS",
            },
            REWARD: {
                CLAIM: "REWARD_CLAIM",
                REFUND: "REWARD_CLAIM_REFUND",
            }
        },
        CALCULATION_TYPE: {
            SAP: {
                PAYMENT: {
                    "0_30_DAYS": "0_30_DAYS_PAYMENT",
                    "31_60_DAYS": "31_60_DAYS_PAYMENT",
                    "61_90_DAYS": "61_90_DAYS_PAYMENT",
                    "91_120_DAYS": "91_120_DAYS_PAYMENT",
                    "120_DAYS_ABOVE": "120_DAYS_ABOVE_PAYMENT",
                },
            }
        }
    },
    REWARD: {
        TYPE: {
            FROM_PRODUCT: "FROM_PRODUCT",
            CUSTOM: "CUSTOM",
        },
        CLAIM: {
            STATUS: {
                PENDING: "PENDING",
                PROCESSING: "PROCESSING",
                SHIPPED: "SHIPPED",
                DELIVERED: "DELIVERED",
                CANCELLED: "CANCELLED",
            }
        }
    }
}

exports.ENTITY_UPDATE_STATUS = {
    ACTIVE: "ACTIVE",
    INACTIVE: "INACTIVE",
    NOT_CHANGED_ACTIVE: "NOT_CHANGED_ACTIVE",
    NOT_CHANGED_INACTIVE: "NOT_CHANGED_INACTIVE",
}

exports.MONGODB = {
    OPERATION_TYPE: {
        INSERT: "insert",
        UPDATE: "update",
    }
}

exports.NOTIFICATION = {
    TYPE: {
        NEW_PAYMENT: "NEW_PAYMENT",
        NEW_PRODUCTS: "NEW_PRODUCTS",
        ORDER_FOR_APPROVAL: "ORDER_FOR_APPROVAL",
        ORDER_APPROVED: "ORDER_APPROVED",
        ORDER_RECEIVED: "ORDER_RECEIVED",
        ORDER_SHIPPED: "ORDER_SHIPPED",
        ORDER_PREPARING: "ORDER_PREPARING",
        ORDER_DELIVERED: "ORDER_DELIVERED",
        DEALS_UPDATES: "DEALS_UPDATES",
        REWARD_PAYMENT: "REWARD_PAYMENT",
        REWARD_PURCHASE: "REWARD_PURCHASE",
        REWARD_MEMBER_SCAN: "REWARD_MEMBER_SCAN",
        REWARD_MILESTONE_ONE: "REWARD_MILESTONE_ONE",
        REWARD_MILESTONE_TWO: "REWARD_MILESTONE_TWO",
        REWARD_MILESTONE_THREE: "REWARD_MILESTONE_THREE",
    }
}

exports.TENANT_USER_SORT_BY = {
    FIRST_NAME: "user.first_name",
    LAST_NAME: "user.last_name",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
}

exports.SORT_TYPE = {
    ASC: "ASC",
    DESC: "DESC"
}

exports.PROMISE_STATES = {
    PENDING: "pending",
    FULFILLED: "fulfilled",
    REJECTED: "rejected",
}
