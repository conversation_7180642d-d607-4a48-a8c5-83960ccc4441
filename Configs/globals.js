global.fetch = require('node-fetch');

global.STATUS_CODES = require('./constants').STATUS_CODES
global.upload = require('./awsUploader').upload

global.languageDB = require("./languageDB").languageDB;
global.mongoose = require('./database').mongoose;
global.autoIncrement = require('./database').autoIncrement;

global.moment = require('moment');
global.momentTimezone = require("moment-timezone")

global.userPool = require('./awsUserPool').userPool;
global.AWSAdminCognito = require('./awsUserPool').AWSAdminCognito

global.AmazonCognitoIdentity = require('amazon-cognito-identity-js');
global.isEmpty = require('lodash.isempty')
// global.momentTZ = require('moment-timezone');
