const mongooseConnect = require('mongoose');

const dbName = process.env.LANGUAGE_DB_NAME

mongooseConnect.createConnection(process.env.PROD_MONGODB_URL, {
    dbName,
})
    .asPromise()
    .then((mongoose) => {
        const message = `${dbName} database connected successfully :)\n`

        console.log(message)
        logger.info(message)
    })
    .catch(err => logger.error(err));

module.exports.languageDB = mongooseConnect
