/* 
HOW TO USE THIS TO SEND RESPONSE

In your controller you can use

res.handler.*function*(data object*, message* , error*)
Ex : 
res.handler.success()
res.handler.success({userName : "<PERSON>"})
res.handler.success({userName : "<PERSON>"}, "User created")
res.handler.success(undefined, "User created")
res.handler.serverError(error object)

for message you can pass simple string
1. We have sent an email to your account
or for with values like
We have sent an email to %s,
{
    key : "TRANSLATION KEY",
    value : "value of %s"
}
*/

const UserRoleDeviceModel = new (require("../Models/UserRoleDeviceModel"))()

const { prepareApiLog } = require("../Utils/helpers");

class ResponseHandler {
    constructor(req, res) {
        this.req = req;
        this.res = res;
    }

    sender(code, message, data, error) {
        const isErrorStatusCode =
            String(code)?.length === 3 &&
            !["1", "2", "3"].includes(String(code).charAt(0))

        if (isErrorStatusCode) {
            const apiInfo = prepareApiLog(this.req, data, error)
            logger.error(message, { apiInfo })
        }

        if (error) {
            if (error.code === "LIMIT_FILE_SIZE")
                return this.res
                    .status(STATUS_CODES.NOT_ALLOWED)
                    .json({
                        message: error.message
                    })
        }

        this.res
            .status(code)
            .json({
                message,
                data
            })
    }

    /* 
        ARGUMENTS : Status code, message, data object,  error object
    */
    custom(...args) { this.sender(...args) }

    /* 
        ARGUMENTS : data o̥̥bject, message, error object
    */

    // 2XX SUCCESS
    success(message, data) {
        this.sender(
            STATUS_CODES.SUCCESS,
            message || 'status_success',
            data
        )
    }

    created(message, data) {
        this.sender(
            STATUS_CODES.CREATED,
            message || 'status_created',
            data
        )
    }

    // 4XX CLIENT ERROR
    badRequest(message, data, error) {
        this.sender(
            STATUS_CODES.BAD_REQUEST,
            message || 'status_bad_request',
            data,
            error,
        )
    }

    unauthorized(message, data, error) {
        this.sender(
            STATUS_CODES.UNAUTHORIZED,
            message || 'status_unauthorized',
            data,
            error,
        ); // Do not remove this semicolon — it's required to prevent a syntax error due to the following IIFE

        // Run async cleanup in background
        (async (req) => {
            try {
                const deviceType = req.headers.devicetype
                const deviceToken = req.headers.devicetoken

                if (deviceType && deviceToken) {
                    const res = await UserRoleDeviceModel.deleteDevicesByFilter({
                        "device_type": deviceType,
                        "device_token": deviceToken,
                    })
                    logger.info("[ResponseHandler > unauthorized]: Device deleted successfully", res)
                }
                else {
                    logger.error("[ResponseHandler > unauthorized]: Device type or token not found in headers", {
                        userRoleId: req.headers.userroleid,
                        deviceType,
                        deviceToken,
                    })
                }
            }
            catch (error) {
                logger.error("[ResponseHandler > unauthorized]:Error while deleting device:", error)
            }
        })(this.req)
    }

    forbidden(message, data, error) {
        this.sender(
            STATUS_CODES.FORBIDDEN,
            message || 'status_forbidden',
            data,
            error,
        )
    }

    notFound(message, data, error) {
        this.sender(
            STATUS_CODES.NOT_FOUND,
            message || 'status_not_found',
            data,
            error,
        )
    }

    conflict(message, data, error) {
        this.sender(
            STATUS_CODES.CONFLICT,
            message || 'status_conflict',
            data,
            error,
        )
    }

    preconditionFailed(message, data, error) {
        this.sender(
            STATUS_CODES.PRECONDITION_FAILED,
            message || 'status_precondition_failed',
            data,
            error,
        )
    }

    validationError(message, error) {
        this.sender(
            STATUS_CODES.VALIDATION_ERROR,
            message || 'status_validation_error',
            undefined,
            error
        )
    }

    // 5XX SERVER ERROR
    serverError(error, data) {
        this.sender(
            STATUS_CODES.SERVER_ERROR,
            'status_server_error',
            data,
            error,
        )
    }

    notAllowed(message, error) {
        this.sender(
            STATUS_CODES.NOT_ALLOWED,
            message || 'status_not_allowed',
            undefined,
            error
        )
    }

    serviceUnavailable(message, error) {
        this.sender(
            STATUS_CODES.SERVICE_UNAVAILABLE,
            message || 'status_service_unavailable',
            undefined,
            error
        )
    }
}

module.exports = ResponseHandler;

