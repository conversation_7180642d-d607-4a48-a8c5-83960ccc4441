const InternalServiceModal = new (require("../Models/InternalServiceModal"))()
const TenantPortalModal = new (require("../Models/tenantPortal"))();
const UserAuthModal = new (require("../Models/auth"))();
const RoleModal = new (require("../Models/roles"));
const CommonModal = new (require("../Models/common"));
const SystemPortalModal = new (require("../Models/systemPortal"))()
const HoldReasonModel = new (require('../Models/HoldReasonModel'))();
const IntegrationCredentialModel = new (require('../Models/IntegrationCredentialModel'))();
const NotificationModel = new (require('../Models/Notification/NotificationModel'))();
const SAPServiceModel = new (require('../Models/SAPServiceModel'))();

const smsHandler = new (require("../Configs/smsHandler"))();
const NotificationManager = new (require("../Managers/NotificationManager"))();
const TemplateService = new (require("../Services/TemplateService"))();

const {
    roundOf,
    toLeanOption,
    getMobileNumber,
    formatAmount,
    stringifyObjectId,
} = require("../Utils/helpers");

const {
    ORDER_STATUS_TYPES,
    PRIMITIVE_ROLES,
    VALUES,
    INTEGRATION_CHANNELS,
    NOTIFICATION,
    TENANT_SERVICES,
    SAP_SERVICE,
    PROMISE_STATES,
} = require("../Configs/constants");

class InternalServiceController {

    async getTenantInfo(req, res) {
        try {
            let { _id, projection, options, populate } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (Array.isArray(populate)) {
                populate = populate.map(stringifiedObj => {
                    return typeof (stringifiedObj) === "string"
                        ? JSON.parse(stringifiedObj)
                        : stringifiedObj
                })
            }
            const tenant = await SystemPortalModal.getTenantsById(_id, projection, options, populate)

            return res.handler.success("tenant_get_success", tenant)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getTenantsInfo(req, res) {
        try {
            let { filter, projection, options } = req.query
            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }
            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }
            const tenants = await SystemPortalModal.getTenants(filter, projection, options)

            return res.handler.success("tenant_get_success", tenants)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getCountries(req, res) {
        try {
            let { filter, projection, options } = req.query
            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }
            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const countries = await CommonModal.findCountries(filter, projection, options)
            return res.handler.success(undefined, countries)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getSalespersons(req, res) {
        try {
            let { filter, projection, options } = req.query
            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }
            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const roleId = await TenantPortalModal.getSalesPersonRole()
            filter.role_id = roleId

            const users = await TenantPortalModal.getTenantUsers(filter, projection, options)

            return res.handler.success(null, users)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async updateTenantInfo(req, res) {
        try {
            const { _id, incObj } = req.body
            const tenant = await InternalServiceModal.updateTenantById(_id, incObj)

            if (tenant.modifiedCount) {
                return res.handler.success("tenant_update_success", tenant)
            }
            return res.handler.success("tenant_update_unsuccess", tenant)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getTenantAppSettings(req, res) {
        try {
            const { _id, projection, options } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }
            const tenantAppSetting = await TenantPortalModal.tenantAppSettingExist(_id, projection, options)

            return res.handler.success(null, tenantAppSetting)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getTenantsAppSettings(req, res) {
        try {
            let { filter, projection, options } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }
            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const tenantsAppSetting = await TenantPortalModal.tenantsAppSettingExist(
                filter,
                projection,
                options
            )
            return res.handler.success(null, tenantsAppSetting)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getUserRoleSettings(req, res) {
        try {
            let { filter, projection, options } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }
            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const userRoleSettings = await TenantPortalModal.findUserRoleSettings(
                filter,
                projection,
                options
            )
            return res.handler.success(null, userRoleSettings)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getUserRoleSettingsCount(req, res) {
        try {
            let { filter } = req.query

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const count = await TenantPortalModal.getUserRoleSettingsCount(filter)
            return res.handler.success(null, count)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async findCurrentPrefix(req, res) {
        try {
            const body = req.query;
            let currentPrefix = await TenantPortalModal.findTenantOrderPrefix({ is_current: true, tenant_id: body.tenantId });
            if (!currentPrefix) {
                const prefix = `${body.tenantId}_`;
                currentPrefix = await TenantPortalModal.findTenantOrderPrefix({ _id: prefix });
                if (!currentPrefix) {
                    currentPrefix = await TenantPortalModal.createTenantPrefix();
                    currentPrefix._id = prefix;
                    currentPrefix.is_current = true;
                    currentPrefix.tenant_id = body.tenantId;
                    currentPrefix.created_by = req.headers.userDetails?._id;
                    currentPrefix.updated_by = req.headers.userDetails?._id;
                }
            }
            currentPrefix.counter += 1;
            await currentPrefix.save();
            // TenantPortalModal.createTenantPrefix
            return res.handler.success("success", currentPrefix);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    //TODO: check profiles with notification_type & allow_email_notification & allow_sms_notification
    async sendOrderEmails(req, res) {
        try {
            const {
                tenantId,
                branchId,

                salesPersonName,
                customerLegalName,
                customerRoleId,
                salesPersonRoleId,

                _id,
                orderId,
                orderDate,
                orderStatusTrack,

                numberOfItems,
                totalQty,
                orderAmount,
                orderStatus,

                shippingAddress,
                shippingCity,
                shippingRegion,
                shippingMobileNumber,
            } = req.body;

            const [tenantDetails, userRoles, roles, decimalPoints] = await Promise.all(
                /* Fetch tenant details */
                TenantPortalModal.getTenantById(
                    tenantId,
                    `
                    country
                    region
                    city
                    primary_contact_id
                    legal_name
                    street_name
                    services
                    enable_sms_services
                    `,
                    {
                        lean: true,
                        populate: [
                            {
                                path: "region",
                                select: { name: 1 }
                            },
                            {
                                path: "city",
                                select: { name: 1 }
                            },
                            {
                                path: "primary_contact_id",
                                select: { country_code: 1, mobile_number: 1 }
                            },
                            {
                                path: "country",
                                select: { currency: 1 }
                            }
                        ],
                    },
                ),

                /* Fetch necessary user roles */
                UserAuthModal.findUserRoles(
                    { _id: { $in: [salesPersonRoleId, customerRoleId] } },
                    `
                    collection_name
                    user_id
                    notifications
                    customer_email
                    customer_legal_name
                    device_access
                    preferred_language
                    `,
                    {
                        lean: true,
                        populate: [
                            {
                                path: "user_id",
                                select: { first_name: 1, last_name: 1, country_code: 1, mobile_number: 1, email: 1 }
                            },
                            {
                                path: "supervisor_id",
                                select: { country_code: 1, mobile_number: 1, _id: 1, email: 1 }
                            }
                        ]
                    }
                ),

                /* Fetch necessary roles */
                RoleModal.getRolesWithFilter(
                    {
                        name: {
                            $in: [
                                PRIMITIVE_ROLES.TENANT_OWNER,
                                PRIMITIVE_ROLES.TENANT_ADMIN,
                                PRIMITIVE_ROLES.BRANCH_MANAGER,
                                PRIMITIVE_ROLES.SUPERVISOR,
                            ]
                        }
                    },
                    "_id name",
                    toLeanOption,
                ),

                /* Fetch tenant's decimal points */
                TenantPortalModal.getAppSettingDecimalPoint(tenantId),
            )

            let customerRoleDetails = {}
            let salesPersonDetails = {}
            let tenantRoles = []
            let branchRole = {}
            let superVisorRole = {}

            userRoles.forEach(userRole => {
                if (userRole.collection_name === "tenant_customers") {
                    customerRoleDetails = userRole
                }
                else {
                    salesPersonDetails = userRole
                }
            })

            roles.forEach(role => {
                if ([PRIMITIVE_ROLES.TENANT_OWNER, PRIMITIVE_ROLES.TENANT_ADMIN].includes(role.name)) {
                    tenantRoles.push(role)
                }
                else if (role.name === PRIMITIVE_ROLES.BRANCH_MANAGER) {
                    branchRole = role
                }
                else if (role.name === PRIMITIVE_ROLES.SUPERVISOR) {
                    superVisorRole = role
                }
            })

            let status_code;
            let notification_type;
            let emailSubject = "";

            switch (orderStatus) {
                case ORDER_STATUS_TYPES.RECEIVED:
                    if (orderStatusTrack[0]?.order_status === ORDER_STATUS_TYPES.PENDING) {
                        notification_type = NOTIFICATION.TYPE.ORDER_APPROVED;
                    }
                    else {
                        notification_type = NOTIFICATION.TYPE.ORDER_RECEIVED;
                    }

                    status_code = 0;
                    emailSubject = "Order Received";
                    break;

                case ORDER_STATUS_TYPES.PREPARING:
                    status_code = 1;
                    notification_type = NOTIFICATION.TYPE.ORDER_PREPARING;
                    emailSubject = "Order Preparing";
                    break;

                case ORDER_STATUS_TYPES.SHIPPED:
                    status_code = 2;
                    notification_type = NOTIFICATION.TYPE.ORDER_SHIPPED
                    emailSubject = "Order Shipped";
                    break;

                case ORDER_STATUS_TYPES.DELIVERED:
                    status_code = 3;
                    notification_type = NOTIFICATION.TYPE.ORDER_DELIVERED;
                    emailSubject = "Order Delivered";
                    break;
            }
            const emails = [];
            const isSmsEnabledForTenant = tenantDetails.enable_sms_services

            const customerNotificationAccess = customerRoleDetails?.notifications?.find(notification => {
                return notification.notification_type == notification_type
            })

            if (customerRoleDetails?.customer_email) {
                emails.push({
                    email: customerRoleDetails.customer_email,
                    emailSubject: `${emailSubject} - ${orderId}`,
                    token: customerRoleDetails._id,
                    roleType: PRIMITIVE_ROLES.CUSTOMER,
                })
            }

            const salesPersonNotificationAccess = salesPersonDetails?.notifications?.find(notification => {
                return notification.notification_type == notification_type
            })
            if (
                salesPersonDetails?.user_id?.email &&
                (salesPersonNotificationAccess?.allow_email_notification ?? true)
            ) {
                emails.push({
                    email: salesPersonDetails.user_id.email,
                    emailSubject: `${emailSubject} - ${orderId} - ${`${salesPersonDetails?.user_id?.first_name || ""} ${salesPersonDetails?.user_id?.last_name || ""}`}`,
                    token: salesPersonDetails._id,
                    roleType: PRIMITIVE_ROLES.SALES_PERSON,
                })
            }

            const orderInfo = {
                orderId,
                salesPersonName,
                customerLegalName: customerLegalName || customerRoleDetails?.customer_legal_name || "",
                amount: roundOf(orderAmount, decimalPoints, "string"),
                currency: tenantDetails.country.currency
            }

            const customerTemplate = TemplateService.getOrderNotificationTemplate(
                notification_type,
                PRIMITIVE_ROLES.CUSTOMER,
                orderInfo
            )
            const salesPersonTemplate = TemplateService.getOrderNotificationTemplate(
                notification_type,
                PRIMITIVE_ROLES.CUSTOMER,
                orderInfo
            )
            const supervisorTemplate = TemplateService.getOrderNotificationTemplate(
                notification_type,
                PRIMITIVE_ROLES.CUSTOMER,
                orderInfo
            )

            const commonNotificationData = {
                tenant_id: tenantId,
                type: notification_type,
                thread_id: _id,
                payload_data: {
                    _id,
                    orderId,
                    orderDate,
                    numberOfItems,
                    totalQty,
                    orderAmount,
                    orderStatus,
                }
            }

            let smsTasks = [];
            const notificationsData = []

            // fetch supervisor notification setting ( fetch supervisor role )
            if (salesPersonDetails.supervisor_id?._id) {
                const superVisorProfile = await UserAuthModal.getUserRoleWithPopulation(
                    {
                        user_id: salesPersonDetails.supervisor_id._id,
                        is_deleted: false,
                        is_active: true,
                        role_id: superVisorRole._id
                    },
                    { notifications: 1 }
                )

                if (superVisorProfile) {
                    const superVisorNotificationAccess = superVisorProfile.notifications.find(notification => {
                        return notification.notification_type == notification_type
                    })

                    notificationsData.push({
                        user_role_id: superVisorProfile._id,
                        title: supervisorTemplate.title,
                        message: supervisorTemplate.message,
                        secondaryLanguageTitle: supervisorTemplate.secondaryLanguageTitle,
                        secondaryLanguageMessage: supervisorTemplate.secondaryLanguageMessage,
                        centerMessage: supervisorTemplate.centerMessage,
                        centerSecondaryLanguageMessage: supervisorTemplate.centerSecondaryLanguageMessage,
                        sendPush: superVisorNotificationAccess?.allow_push_notification ?? true,
                    })

                    if (superVisorNotificationAccess?.allow_email_notification ?? true) {
                        emails.push({
                            email: salesPersonDetails.supervisor_id.email,
                            emailSubject: `${emailSubject} - ${orderId} - ${`${salesPersonDetails?.user_id?.first_name || ""} ${salesPersonDetails?.user_id?.last_name || ""}`}`,
                            token: superVisorProfile._id,
                        });
                    }
                    const supervisorMobileNumber = getMobileNumber(salesPersonDetails.supervisor_id)

                    if (supervisorMobileNumber && (superVisorNotificationAccess?.allow_sms_notification ?? true)) {
                        smsTasks.push({
                            userRoleId: superVisorProfile._id,
                            mobileNumber: supervisorMobileNumber,
                            message: encodeURIComponent(supervisorTemplate.smsBody)
                        })
                    }
                }
            }

            // Tenant Owner and Admins profiles
            const tenantEmailProfiles = await CommonModal.getCommonProfilesForOrderEmail(tenantId, tenantRoles, branchId, branchRole)

            if (Array.isArray(tenantEmailProfiles)) {
                for (let i = 0; i < tenantEmailProfiles.length; i++) {
                    const em = tenantEmailProfiles[i];
                    const emNotificationAccess = em.notifications.find(notification => {
                        return notification.notification_type == notification_type
                    })

                    notificationsData.push({
                        user_role_id: em._id,
                        title: supervisorTemplate.title,
                        message: supervisorTemplate.message,
                        secondaryLanguageTitle: supervisorTemplate.secondaryLanguageTitle,
                        secondaryLanguageMessage: supervisorTemplate.secondaryLanguageMessage,
                        centerMessage: supervisorTemplate.centerMessage,
                        centerSecondaryLanguageMessage: supervisorTemplate.centerSecondaryLanguageMessage,
                        sendPush: emNotificationAccess?.allow_push_notification ?? true,
                    })

                    if (emNotificationAccess?.allow_email_notification ?? true) {
                        emails.push({
                            email: em.user_id?.email,
                            emailSubject: `${emailSubject} - ${orderId} - ${`${salesPersonDetails?.user_id?.first_name || ""} ${salesPersonDetails?.user_id?.last_name || ""}`}`,
                            token: em._id,
                        });
                    }
                    const mobileNumber = getMobileNumber(em.user_id)

                    if (mobileNumber && (emNotificationAccess?.allow_sms_notification ?? true)) {
                        smsTasks.push({
                            userRoleId: em._id,
                            mobileNumber,
                            message: encodeURIComponent(supervisorTemplate.smsBody)
                        })
                    }
                }
            }

            const dynamicLinksResponse = await Promise.allSettled(
                emails.map(({ token, roleType }) => {
                    return CommonModal.generateDynamicLink({ _id, token, roleType })
                        .then(value => ({
                            status: PROMISE_STATES.FULFILLED,
                            value,
                            token
                        }))
                        .catch(reason => ({
                            status: PROMISE_STATES.REJECTED,
                            reason,
                            token
                        }))
                })
            )

            const dynamicLinkByToken = {}

            // Now you can clearly track each result with token
            dynamicLinksResponse.forEach(result => {
                if (result.status === PROMISE_STATES.FULFILLED) {
                    const { value, token } = result.value;
                    console.log(`✅ Link generated for token ${token}:`, value);
                    dynamicLinkByToken[stringifyObjectId(token)] = value
                }
                else {
                    const { reason, token } = result.reason;
                    console.error(`❌ Failed to generate link for token ${token}:`, reason);
                }
            })

            const emailSendData = []

            let customerLink = `${process.env.REACT_APP_URL}order-link/${_id}?token=${customerRoleDetails._id}`
            let userLink = `${process.env.REACT_APP_URL}order-link/${_id}`

            for (let i = 0; i < emails.length; i++) {
                const {
                    token,
                    roleType,
                    email,
                    emailSubject,
                } = emails[i]

                let orderLink = dynamicLinkByToken[stringifyObjectId(token)]

                if (roleType === PRIMITIVE_ROLES.CUSTOMER && !orderLink) {
                    orderLink = customerLink
                }
                else if (roleType === PRIMITIVE_ROLES.SALES_PERSON && !orderLink) {
                    orderLink = userLink
                }

                emailSendData.push({
                    items: {
                        _id,
                        order_id: orderId,
                        order_date: moment(orderDate).format("MMMM D, YYYY"),
                        num_of_items: numberOfItems,
                        total_qty: totalQty,
                        order_amount: formatAmount(orderAmount, decimalPoints),
                        status: ["Received", "Preparing", "Shipped", "Delivered"],
                        status_code,

                        company_legal_name: tenantDetails.legal_name,
                        company_address: tenantDetails.street_name,
                        company_city: tenantDetails.city?.name,
                        company_region: tenantDetails.region?.name,
                        company_phone: `${tenantDetails.primary_contact_id?.country_code || ""} ${tenantDetails.primary_contact_id?.mobile_number || ""}`,

                        shipping_address: shippingAddress,
                        shipping_city: shippingCity,
                        shipping_region: shippingRegion,
                        shipping_mobile_number: shippingMobileNumber,

                        sales_person_mobile: `${salesPersonDetails?.user_id?.country_code || ""} ${salesPersonDetails?.user_id?.mobile_number || ""}`,
                        salesperson_name: salesPersonName,

                        customer_legal_name: customerLegalName || customerRoleDetails.customer_legal_name,

                        rtl: false,
                        order_link: orderLink,
                    },
                    language: "en",
                    email,
                    subject: emailSubject
                })
            }

            const emailSummary = await Promise.allSettled(
                emailSendData.map(({ items, language, email, subject }) => {
                    return UserAuthModal.orderInfoEmail(items, language, email, subject)
                })
            )

            emailSummary.forEach(summary => {
                if (summary.reason) {
                    logger.error(summary.reason)
                }
            })

            notificationsData.push(
                {
                    user_role_id: customerRoleId,
                    title: customerTemplate.title,
                    message: customerTemplate.message,
                    secondaryLanguageTitle: customerTemplate.secondaryLanguageTitle,
                    secondaryLanguageMessage: customerTemplate.secondaryLanguageMessage,
                    centerMessage: customerTemplate.centerMessage,
                    centerSecondaryLanguageMessage: customerTemplate.centerSecondaryLanguageMessage,
                    sendPush: customerNotificationAccess?.allow_push_notification ?? true,
                },
                {
                    user_role_id: salesPersonRoleId,
                    title: salesPersonTemplate.title,
                    message: salesPersonTemplate.message,
                    secondaryLanguageTitle: salesPersonTemplate.secondaryLanguageTitle,
                    secondaryLanguageMessage: salesPersonTemplate.secondaryLanguageMessage,
                    centerMessage: salesPersonTemplate.centerMessage,
                    centerSecondaryLanguageMessage: salesPersonTemplate.centerSecondaryLanguageMessage,
                    sendPush: salesPersonNotificationAccess?.allow_push_notification ?? true,
                }
            )

            const isPushEnabledForTenant = tenantDetails.services.some(service => {
                return (
                    service.key === TENANT_SERVICES.PUSH_NOTIFICATION &&
                    service.permission.create
                )
            }) && VALUES.IS_DEV_ENV //FIX:ME Remove environment condition when notification moved to production/staging

            if (isSmsEnabledForTenant) {
                const customerMobileNumber = getMobileNumber(customerRoleDetails.user_id);
                const salesPersonMobileNumber = getMobileNumber(salesPersonDetails.user_id);

                if (customerMobileNumber && (customerNotificationAccess?.allow_sms_notification ?? true)) {
                    smsTasks.push({
                        userRoleId: customerRoleId,
                        mobileNumber: customerMobileNumber,
                        message: encodeURIComponent(customerTemplate.smsBody)
                    })
                }

                if (salesPersonMobileNumber && (salesPersonNotificationAccess?.allow_sms_notification ?? true)) {
                    smsTasks.push({
                        userRoleId: salesPersonRoleId,
                        mobileNumber: salesPersonMobileNumber,
                        message: encodeURIComponent(salesPersonTemplate.smsBody)
                    })
                }
            }

            const fullIds = [];
            const pushNotificationList = [];

            try {
                const rawData = [];

                for (const notification of notificationsData) {
                    const {
                        user_role_id,
                        title,
                        message,
                        secondaryLanguageTitle,
                        secondaryLanguageMessage,
                        centerMessage,
                        centerSecondaryLanguageMessage,
                        sendPush,
                    } = notification;

                    rawData.push({
                        user_role_id,
                        title,
                        message,
                        secondary_language_title: secondaryLanguageTitle,
                        secondary_language_message: secondaryLanguageMessage,
                        center_message: centerMessage,
                        center_secondary_language_message: centerSecondaryLanguageMessage,
                        ...commonNotificationData
                    });

                    if (isPushEnabledForTenant && sendPush) {
                        fullIds.push(`${tenantId}_${user_role_id}`);

                        pushNotificationList.push({
                            userRoleId: user_role_id,
                            title,
                            message,
                            secondaryLanguageTitle,
                            secondaryLanguageMessage
                        });
                    }
                }

                await NotificationModel.bulkCreateNotifications(rawData);
            }
            catch (error) {
                logger.error(error, {
                    errorMessage: "COULD NOT CREATE NOTIFICATION IN DB"
                });
            }

            if (pushNotificationList.length > 0) {
                try {
                    const userSettings = await TenantPortalModal.findUserRoleSettings(
                        { _id: { $in: fullIds } },
                        { preferred_language: 1 },
                        toLeanOption,
                    );

                    const userSettingsMap = userSettings.reduce((map, { _id, preferred_language }) => {
                        const roleId = _id.replace(`${tenantId}_`, '');
                        map[roleId] = preferred_language;
                        return map;
                    }, {});

                    const pushNotificationPayload = pushNotificationList.map(data => ({
                        ...data,
                        notificationLanguage: userSettingsMap[data.userRoleId]
                    }));

                    const summary = await NotificationManager.sendPushNotificationWithUserRoleIds(
                        pushNotificationPayload,
                        commonNotificationData.type,
                        commonNotificationData.payload_data,
                        commonNotificationData.thread_id
                    );

                    smsTasks = smsTasks.filter(({ userRoleId }) =>
                        !summary.success.includes(userRoleId)
                    );
                }
                catch (error) {
                    logger.error(error, {
                        errorMessage: "COULD NOT SEND PUSH NOTIFICATION"
                    });
                }
            }

            if (isSmsEnabledForTenant) {
                const smsSummary = await Promise.allSettled(smsTasks.map(info => {
                    return smsHandler.sendSMS(info.mobileNumber, info.message)
                }))

                smsSummary.forEach(summary => {
                    if (summary.reason) {
                        logger.error(summary.reason)
                    }
                })
            }

            return res.handler.success();
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async sendNewOrderEmail(req, res) {
        try {
            let notification_type = NOTIFICATION.TYPE.ORDER_FOR_APPROVAL;
            let emailSubject = "New Order To Process";

            const {
                tenantId,
                branchId,

                salesPersonName,
                customerLegalName,
                customerRoleId,
                salesPersonRoleId,

                _id,
                orderId,
                orderDate,
                numberOfItems,
                totalQty,
                orderAmount,
                orderStatus,
            } = req.body;

            const customerPopulationArray = [
                {
                    path: "user_id",
                    select: { country_code: 1, mobile_number: 1 }
                },
            ];
            const salesPersonPopulationArray = [
                {
                    path: "user_id",
                    select: { first_name: 1, last_name: 1, country_code: 1, mobile_number: 1, email: 1 }
                },
                {
                    path: "supervisor_id",
                    select: { country_code: 1, mobile_number: 1, _id: 1, email: 1 }
                }
            ];

            const tenantPopulationArray = [
                {
                    path: "country",
                    select: { currency: 1 }
                }
            ];

            const promises = [
                TenantPortalModal.getTenantByFilter({ _id: tenantId }, undefined, { lean: true }, tenantPopulationArray),
                UserAuthModal.getUserRoleWithPopulation({ _id: customerRoleId }, { customer_email: 1, customer_legal_name: 1, customer_id: 1, user_id: 1, collection_name: 1, notifications: 1 }, { lean: true }, customerPopulationArray),
                UserAuthModal.getUserRoleWithPopulation({ _id: salesPersonRoleId }, { user_id: 1, settings: 1, collection_name: 1, notifications: 1 }, { lean: true }, salesPersonPopulationArray),
                // Get roles
                RoleModal.getRolesWithFilter({ name: { $in: [PRIMITIVE_ROLES.TENANT_OWNER, PRIMITIVE_ROLES.TENANT_ADMIN] } }, { _id: 1 }),
                RoleModal.getRoleByFilter({ name: PRIMITIVE_ROLES.BRANCH_MANAGER }, { _id: 1 }),
                RoleModal.getRoleByFilter({ name: PRIMITIVE_ROLES.SUPERVISOR }, { _id: 1 }),
                TenantPortalModal.tenantAppSettingExist(tenantId),
            ];

            const [tenantDetails, customerRoleDetails, salesPersonDetails, tenantRoles, branchRole, superVisorRole, appSettings] = await Promise.all(promises);

            const userRoles = await CommonModal.getCommonProfilesForOrderEmail(tenantId, tenantRoles, branchId, branchRole)

            const emailPromises = [];

            const orderInfo = {
                orderId,
                salesPersonName,
                customerLegalName: customerLegalName || customerRoleDetails?.customer_legal_name || "",
                amount: roundOf(orderAmount, appSettings.decimal_points, "string"),
                currency: tenantDetails.country.currency
            }

            const customerTemplate = TemplateService.getOrderNotificationTemplate(
                notification_type,
                PRIMITIVE_ROLES.CUSTOMER,
                orderInfo
            )
            const salesPersonTemplate = TemplateService.getOrderNotificationTemplate(
                notification_type,
                PRIMITIVE_ROLES.CUSTOMER,
                orderInfo
            )
            const supervisorTemplate = TemplateService.getOrderNotificationTemplate(
                notification_type,
                PRIMITIVE_ROLES.CUSTOMER,
                orderInfo
            )

            const commonNotificationData = {
                tenant_id: tenantId,
                type: notification_type,
                thread_id: _id,
                payload_data: {
                    _id,
                    orderId,
                    orderDate,
                    numberOfItems,
                    totalQty,
                    orderAmount,
                }
            }

            let smsTasks = [];
            const notificationsData = []

            userRoles.forEach(userRoleInfo => {
                const notificationAccess = userRoleInfo.notifications.find(notification => {
                    return notification.notification_type == notification_type
                })

                if (notificationAccess?.allow_email_notification ?? true) {
                    emailPromises.push(
                        UserAuthModal.newOrderEmail(
                            {
                                _id,
                                order_id: orderId,
                                order_date: moment(orderDate).format("MMMM D, YYYY"),
                                num_of_items: numberOfItems,
                                total_qty: totalQty,
                                order_amount: roundOf(orderAmount, appSettings.decimal_points, "string"),
                                sales_person: salesPersonName,

                                cust_name: customerLegalName || customerRoleDetails.customer_legal_name,
                                cust_id: customerRoleDetails.customer_id,

                                rtl: false,
                                order_link: CommonModal.generateProcessOrderDynamicLink({ _id, token: userRoleInfo._id })
                            },
                            "en",
                            userRoleInfo.user_id?.email,
                            `${emailSubject} - ${orderId} - ${`${salesPersonDetails?.user_id?.first_name || ""} ${salesPersonDetails?.user_id?.last_name || ""}`}`
                        )
                    );
                }

                if ((notificationAccess?.allow_sms_notification ?? true) && userRoleInfo.user_id) {
                    const mobileNumber = (userRoleInfo.user_id.country_code + userRoleInfo.user_id.mobile_number).replace("+", "")

                    smsTasks.push({
                        userRoleId: userRoleInfo._id,
                        mobileNumber,
                        message: encodeURIComponent(salesPersonTemplate.smsBody)
                    })
                }

                notificationsData.push({
                    user_role_id: userRoleInfo._id,
                    title: supervisorTemplate.title,
                    message: supervisorTemplate.message,
                    secondaryLanguageTitle: supervisorTemplate.secondaryLanguageTitle,
                    secondaryLanguageMessage: supervisorTemplate.secondaryLanguageMessage,
                    centerMessage: supervisorTemplate.centerMessage,
                    centerSecondaryLanguageMessage: supervisorTemplate.centerSecondaryLanguageMessage,
                    sentPush: notificationAccess?.allow_push_notification ?? true,
                })
            });

            const emailSummary = await Promise.allSettled(emailPromises)
            emailSummary.forEach(summary => {
                if (summary.reason) {
                    logger.error(summary.reason)
                }
            })

            const isPushEnabledForTenant = tenantDetails.services.some(service => {
                return (
                    service.key === TENANT_SERVICES.PUSH_NOTIFICATION &&
                    service.permission.create
                )
            }) && VALUES.IS_DEV_ENV //FIX:ME Remove environment condition when notification moved to production/staging

            const isSmsEnabledForTenant = tenantDetails.enable_sms_services

            const customerMobileNumber = getMobileNumber(customerRoleDetails.user_id);
            const salesPersonMobileNumber = getMobileNumber(salesPersonDetails.user_id);

            const customerNotificationAccess = customerRoleDetails?.notifications.find(notification => {
                return notification.notification_type == notification_type
            })

            notificationsData.push({
                user_role_id: customerRoleId,
                title: customerTemplate.title,
                message: customerTemplate.message,
                secondaryLanguageTitle: customerTemplate.secondaryLanguageTitle,
                secondaryLanguageMessage: customerTemplate.secondaryLanguageMessage,
                centerMessage: customerTemplate.centerMessage,
                centerSecondaryLanguageMessage: customerTemplate.centerSecondaryLanguageMessage,
                sentPush: customerNotificationAccess?.allow_push_notification ?? true,
            })

            if (customerMobileNumber && (customerNotificationAccess?.allow_sms_notification ?? true)) {
                smsTasks.push({
                    userRoleId: customerRoleId,
                    mobileNumber: customerMobileNumber,
                    message: encodeURIComponent(customerTemplate.smsBody)
                })
            }

            const salesPersonNotificationAccess = salesPersonDetails?.notifications.find(notification => {
                return notification.notification_type == notification_type
            })

            notificationsData.push({
                user_role_id: salesPersonRoleId,
                title: salesPersonTemplate.title,
                message: salesPersonTemplate.message,
                secondaryLanguageTitle: salesPersonTemplate.secondaryLanguageTitle,
                secondaryLanguageMessage: salesPersonTemplate.secondaryLanguageMessage,
                centerMessage: salesPersonTemplate.centerMessage,
                centerSecondaryLanguageMessage: salesPersonTemplate.centerSecondaryLanguageMessage,
                sentPush: salesPersonNotificationAccess?.allow_push_notification ?? true,
            })

            if (salesPersonMobileNumber && (salesPersonNotificationAccess?.allow_sms_notification ?? true)) {
                smsTasks.push({
                    userRoleId: salesPersonRoleId,
                    mobileNumber: salesPersonMobileNumber,
                    message: encodeURIComponent(salesPersonTemplate.smsBody)
                })
            }

            if (salesPersonDetails.supervisor_id?._id) {
                const superVisorProfile = await UserAuthModal.getUserRoleWithPopulation(
                    {
                        user_id: salesPersonDetails.supervisor_id._id,
                        is_deleted: false,
                        is_active: true,
                        role_id: superVisorRole._id
                    },
                    { notifications: 1 }
                )
                const supervisorMobileNumber = ((salesPersonDetails.supervisor_id?.country_code) + (salesPersonDetails.supervisor_id?.mobile_number))?.replace("+", "");

                const superVisorNotificationAccess = superVisorProfile.notifications.find(notification => {
                    return notification.notification_type == notification_type
                })

                notificationsData.push({
                    user_role_id: superVisorProfile._id,
                    title: supervisorTemplate.title,
                    message: supervisorTemplate.message,
                    secondaryLanguageTitle: supervisorTemplate.secondaryLanguageTitle,
                    secondaryLanguageMessage: supervisorTemplate.secondaryLanguageMessage,
                    centerMessage: supervisorTemplate.centerMessage,
                    centerSecondaryLanguageMessage: supervisorTemplate.centerSecondaryLanguageMessage,
                    sentPush: superVisorNotificationAccess?.allow_push_notification ?? true,
                })

                if (supervisorMobileNumber && (superVisorNotificationAccess?.allow_sms_notification ?? true)) {
                    smsTasks.push({
                        userRoleId: superVisorProfile._id,
                        mobileNumber: supervisorMobileNumber,
                        message: encodeURIComponent(supervisorTemplate.smsBody)
                    })
                }
            }

            const fullIds = []
            const pushNotificationList = []

            try {
                const rawData = []

                for (const notification of notificationsData) {
                    const {
                        user_role_id,
                        title,
                        message,
                        secondaryLanguageTitle,
                        secondaryLanguageMessage,
                        centerMessage,
                        centerSecondaryLanguageMessage,
                        sentPush,
                    } = notification;

                    rawData.push({
                        user_role_id,
                        title,
                        message,
                        secondary_language_title: secondaryLanguageTitle,
                        secondary_language_message: secondaryLanguageMessage,
                        center_message: centerMessage,
                        center_secondary_language_message: centerSecondaryLanguageMessage,
                        ...commonNotificationData
                    });

                    if (isPushEnabledForTenant && sentPush) {
                        fullIds.push(`${tenantId}_${user_role_id}`);

                        pushNotificationList.push({
                            userRoleId: user_role_id,
                            title,
                            message,
                            secondaryLanguageTitle,
                            secondaryLanguageMessage
                        });
                    }
                }

                await NotificationModel.bulkCreateNotifications(rawData)
            }
            catch (error) {
                logger.error(error, {
                    errorMessage: "COULD NOT ADD NOTIFICATION"
                })
            }

            if (pushNotificationList.length > 0) {
                try {
                    const userSettings = await TenantPortalModal.findUserRoleSettings(
                        { _id: { $in: fullIds } },
                        { preferred_language: 1 },
                        toLeanOption,
                    );

                    const userSettingsMap = userSettings.reduce((map, setting) => {
                        const originalId = setting._id.replace(`${tenantId}_`, '');
                        map[originalId] = setting.preferred_language;
                        return map;
                    }, {});

                    const pushNotificationPayload = pushNotificationList.map(data => ({
                        ...data,
                        notificationLanguage: userSettingsMap[data.userRoleId]
                    }))

                    const summary = await NotificationManager.sendPushNotificationWithUserRoleIds(
                        pushNotificationPayload,
                        commonNotificationData.type,
                        commonNotificationData.payload_data,
                        commonNotificationData.thread_id
                    );

                    smsTasks = smsTasks.filter(({ userRoleId }) =>
                        !summary.success.includes(userRoleId)
                    );
                }
                catch (error) {
                    logger.error(error, {
                        errorMessage: "COULD NOT SEND PUSH NOTIFICATION"
                    });
                }
            }

            if (isSmsEnabledForTenant) {
                const smsSummary = await Promise.allSettled(smsTasks.map(info => {
                    return smsHandler.sendSMS(info.mobileNumber, info.message)
                }))

                smsSummary.forEach(summary => {
                    if (summary.reason) {
                        logger.error(summary.reason)
                    }
                })
            }

            return res.handler.success();
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async updateBulkCustomers(req, res) {
        try {
            const { filter, updateFields } = req.body

            const updateResult = await TenantPortalModal.updateBulkCustomers(filter, updateFields, req.headers)
            if (updateResult.modifiedCount) {
                return res.handler.success("tenant_update_success", updateResult)
            }
            return res.handler.success("tenant_update_unsuccess", updateResult)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getCustomer(req, res) {
        try {
            const { _id, projection, options, populate } = req.body

            const customer = await TenantPortalModal.getCustomer(_id, projection, options, populate)
            return res.handler.success(null, customer)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getCustomers(req, res) {
        try {
            let { filter, projection, options } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const customerRole = await TenantPortalModal.getCustomerRole();

            const customers = await TenantPortalModal.getTenantUsers(
                {
                    role_id: customerRole._id,
                    ...filter
                },
                projection,
                options
            )

            return res.handler.success(null, customers)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async checkCustomersPreApprovedStatus(req, res) {
        try {
            const {
                tenantId,
                externalIds
            } = req.query

            const sapIntegrationCredentials = await IntegrationCredentialModel.getCredential(
                {
                    tenant_id: tenantId,
                    name: INTEGRATION_CHANNELS.SAP_SERVICE
                },
                "configurations is_active",
                {
                    lean: true
                }
            )

            if (!sapIntegrationCredentials) {
                return res.handler.forbidden('sap_integration_not_found')
            }

            if (!sapIntegrationCredentials.is_active) {
                return res.handler.notFound('sap_integration_not_active')
            }

            const tasks = externalIds.map(externalId => {
                return SAPServiceModel.checkPreApproved(
                    externalId,
                    tenantId,
                    sapIntegrationCredentials
                )
            })

            const results = await Promise.all(tasks)

            let data = {}

            for (let i = 0; i < results.length; i++) {
                data[externalIds[i]] = results[i]
            }

            return res.handler.success(undefined, data)
        }
        catch (error) {
            if (error.name === SAP_SERVICE.ERROR.API) {
                return res.handler.custom(
                    error.statuscode,
                    error.message,
                    error.data,
                    error
                )
            }
            else {
                return res.handler.serverError(error);
            }
        }
    }

    async getUsers(req, res) {
        try {
            let { filter, projection, options } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const users = await TenantPortalModal.getTenantUsers(
                filter,
                projection,
                options
            )

            return res.handler.success(null, users)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getCustomersCount(req, res) {
        try {
            let { filter } = req.query

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const customerRole = await TenantPortalModal.getCustomerRole();

            const customersCount = await TenantPortalModal.getTenantUsersCount({
                role_id: customerRole._id,
                ...filter
            })

            return res.handler.success(null, customersCount)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getTenantBranch(req, res) {
        try {
            const branches = await SystemPortalModal.getBranchesWithWareHouse(req.query.tenantId);
            return res.handler.success("tenant_update_unsuccess", branches)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getTenantBranches(req, res) {
        try {
            let { filter, projection, options } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const branches = await SystemPortalModal.getTenantBranches(
                filter,
                projection,
                options
            )
            return res.handler.success(undefined, branches)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async salesPersonsBySupervisorId(req, res) {
        try {
            const { supervisorUserRoleId, tenantId } = req.query;
            const [supervisorProfileInfo, salespersonRole] = await Promise.all(
                [
                    TenantPortalModal.findUserProfileWithFilter({ _id: supervisorUserRoleId }, { user_id: 1 }),
                    RoleModal.getRoleByFilter({ portal_type: VALUES.portals.SALES_APP }, { _id: 1 })
                ])
            const salesPersons = await TenantPortalModal.findUserProfilesWithFilter(
                {
                    role_id: salespersonRole._id,
                    supervisor_id: supervisorProfileInfo.user_id,
                    tenant_id: tenantId
                }, { _id: 1 });
            return res.handler.success(null, salesPersons)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getShippingLabel(req, res) {
        try {
            let { tenantId, projection, options } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }
            const shippingLabel = await TenantPortalModal.shippingLabelExist(tenantId, projection, options)

            if (shippingLabel?.shipping_label_logo) {
                shippingLabel.shipping_label_logo =
                    VALUES.awsPublicBucketBaseURL +
                    "shipping-label" +
                    `/${tenantId}/` +
                    shippingLabel.shipping_label_logo
            }
            return res.handler.success(null, shippingLabel)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async checkOrderUsers(req, res) {
        try {
            let { tenantId, userRoleId, portalType, branchId } = req.query
            let validUser;

            switch (portalType) {
                case VALUES.portals.TENANT_PORTAL:
                    validUser = await TenantPortalModal.findUserProfileWithFilter({ tenant_id: tenantId, _id: userRoleId, is_deleted: false })
                    break;
                case VALUES.portals.BRANCH_PORTAL:
                    validUser = await TenantPortalModal.findUserProfileWithFilter({ tenant_id: tenantId, _id: userRoleId, brach_id: branchId, is_deleted: false })
                    break;
                default:
                    validUser = await TenantPortalModal.findUserProfileWithFilter({ tenant_id: tenantId, _id: userRoleId, is_deleted: false, is_active: true })
                    break;
            }

            validUser = validUser ? true : false;
            const countryInfo = await TenantPortalModal.getTenantByFilter({ _id: tenantId }, { country: 1 }, { populate: { path: 'country', select: { currency: 1, name: 1 } } })

            return res.handler.success(null, { validUser, countryInfo });
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async updateDataSheetFiles(req, res) {
        try {
            const { filter, updateFields } = req.body

            const updateResult = await TenantPortalModal.updateBulkDataSheetFiles(filter, updateFields, req.headers)

            if (updateResult.modifiedCount) {
                return res.handler.success("data_sheet_file_update_success", updateResult)
            }
            return res.handler.success("data_sheet_file_update_unsuccess", updateResult)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    getHoldReasonTemplate = async (req, res) => {
        try {
            let {
                filter = {},
                projection = {},
                options = {},
            } = req.query

            if (typeof (projection) === "string") {
                projection = JSON.parse(projection)
            }

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const holdReason = await HoldReasonModel.getHoldReason(
                filter,
                projection,
                options
            )

            return res.handler.success(null, holdReason);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    getIntegrationCredential = async (req, res) => {
        try {
            let {
                filter = {},
                projection = {},
                options = {},
            } = req.query;

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const integrationCredential = await IntegrationCredentialModel.getCredential(
                filter,
                projection,
                options
            )

            return res.handler.success(null, integrationCredential);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    getIntegrationCredentials = async (req, res) => {
        try {
            let {
                filter = {},
                projection = {},
                options = {},
            } = req.query;

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const integrationCredentials = await IntegrationCredentialModel.getCredentials(
                filter,
                projection,
                options
            )

            return res.handler.success(null, integrationCredentials);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    getRecentlyRestockedItems = async (req, res) => {
        try {
            let {
                sapIntegrationCredentials
            } = req.query

            if (typeof sapIntegrationCredentials === "string") {
                sapIntegrationCredentials = JSON.parse(sapIntegrationCredentials)
            }

            const items = await SAPServiceModel.getRecentlyRestockedItems(sapIntegrationCredentials)
            return res.handler.success(null, items)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

}

module.exports = InternalServiceController
