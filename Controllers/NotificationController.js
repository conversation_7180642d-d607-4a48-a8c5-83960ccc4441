const NotificationModel = new (require("../Models/Notification/NotificationModel"))();
const ScheduledNotificationModel = new (require("../Models/Notification/ScheduledNotificationModel"))();

const TenantPortalModal = new (require("../Models/tenantPortal"))();
const UserAuthModal = new (require("../Models/auth"))()
const SystemPortalModal = new (require("../Models/systemPortal"))()

const NotificationManager = new (require("../Managers/NotificationManager"))()

const smsHandler = new (require("../Configs/smsHandler"))();

const FileUpload = require('../Configs/awsUploader').S3Upload;

const {
    TENANT_SERVICES,
    BUCKET_TYPE,
    FILE_PATH,
    VALUES,
    LANGUAGE,
} = require('../Configs/constants');

const { toLeanOption, getMobileNumber } = require("../Utils/helpers");

class NotificationController {

    listNotifications = async (req, res) => {
        try {
            const notifications = await NotificationModel.findNotificationsWithPagination(req.query)
            if (req.query.page === 1) {
                await NotificationModel.updateNotificationsByFilter(
                    req.query,
                    {
                        is_read: true
                    }
                )
            }
            return res.handler.success(undefined, notifications)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    countUnreadNotifications = async (req, res) => {
        try {
            const unreadNotificationCount = await NotificationModel.countNotificationsByFilter({
                unreadOnly: true,
                ...req.query
            })
            return res.handler.success(undefined, {
                unreadNotificationCount
            })
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    createNotifications = async (req, res) => {
        try {
            await this.sentNotification(req.body)
            return res.handler.success()
        }
        catch (error) {
            logger.error(error)
            return res.handler.serverError(error);
        }
    }

    async sentNotification(body, session) {
        const {
            tenantId,
            userRoleIds,
            title,
            secondaryLanguageTitle,
            message,
            secondaryLanguageMessage,
            smsBody,
            secondaryLanguageSmsBody,
            type,
            threadId,
            payloadData,
            smsRequired,
            sentPushNotification = true,
        } = body
        logger.info("🚀 ~ :86 ~ NotificationController ~ sentNotification ~ body:", body)

        await NotificationModel.addNotifications(body, session)

        const tenantInfo = await TenantPortalModal.getTenantById(
            tenantId,
            {
                services: 1,
                enable_sms_services: 1,
            },
            {
                lean: true,
                session
            }
        )
        logger.info("🚀 ~ :101 ~ NotificationController ~ sentNotification ~ tenantInfo:", tenantInfo)

        const isPushEnabledForTenant = sentPushNotification && tenantInfo.services.some(service => {

            return (
                service.key === TENANT_SERVICES.PUSH_NOTIFICATION &&
                service.permission.create
            )
        }) && VALUES.IS_DEV_ENV //FIX:ME Remove environment condition when notification moved to production/staging

        const isSmsEnabledForTenant = tenantInfo.enable_sms_services && smsBody

        logger.info("🚀 ~ :104 ~ NotificationController ~ sentNotification ~ isSmsEnabledForTenant:", isSmsEnabledForTenant)
        logger.info("🚀 ~ :109 ~ NotificationController ~ sentNotification ~ isPushEnabledForTenant:", isPushEnabledForTenant)

        if (
            !(
                isPushEnabledForTenant ||
                isSmsEnabledForTenant
            )
        ) {
            return
        }

        const population = []
        if (isSmsEnabledForTenant) {
            population.push({
                path: "user_id",
                select: {
                    country_code: 1,
                    mobile_number: 1
                }
            })
        }

        const userRoleInfos = await UserAuthModal.getUserRolesWithPopulation(
            {
                _id: {
                    $in: userRoleIds
                }
            },
            {
                notifications: 1,
                collection_name: 1,
            },
            {
                lean: true,
                session
            },
            population
        )

        const userRoleIdSet = new Set()
        const userRoleIdsForSentPush = []
        let userInfoForSentSms = []

        userRoleInfos.forEach(({ notifications, _id, user_id }) => {
            const notificationAccess = notifications.find(notification => {
                return notification.notification_type == type
            })

            if (
                isPushEnabledForTenant &&
                (notificationAccess?.allow_push_notification ?? true)
            ) {
                userRoleIdsForSentPush.push(_id)
                userRoleIdSet.add(`${tenantId}_${_id}`)
            }

            if (
                isSmsEnabledForTenant &&
                (notificationAccess?.allow_sms_notification ?? true)
            ) {
                userInfoForSentSms.push({
                    _id,
                    user_id,
                })
                userRoleIdSet.add(`${tenantId}_${_id}`)
            }
        })

        const userSettings = await TenantPortalModal.findUserRoleSettings(
            { _id: { $in: Array.from(userRoleIdSet) } },
            { preferred_language: 1 },
            toLeanOption,
        )
        logger.info("🚀 ~ :187 ~ NotificationController ~ sentNotification ~ userSettings:", userSettings)


        const userSettingsMap = userSettings.reduce((map, setting) => {
            const originalId = setting._id.replace(`${tenantId}_`, '')
            map[originalId] = setting.preferred_language
            return map
        }, {})

        logger.info("🚀 ~ :197 ~ NotificationController ~ sentNotification ~ userRoleIdsForSentPush:", userRoleIdsForSentPush)

        if (userRoleIdsForSentPush.length) {
            const pushNotificationPayload = userRoleIdsForSentPush.map(userRoleId => ({
                userRoleId,
                title,
                message,
                secondaryLanguageTitle,
                secondaryLanguageMessage,
                notificationLanguage: userSettingsMap[userRoleId]
            }));
            logger.info("🚀 ~ :207 ~ NotificationController ~ sentNotification ~ pushNotificationPayload:", pushNotificationPayload)


            const summary = await NotificationManager.sendPushNotificationWithUserRoleIds(
                pushNotificationPayload,
                type,
                payloadData,
                threadId,
                session
            );
            logger.info("🚀 ~ :217 ~ NotificationController ~ sentNotification ~ summary:", summary)


            if (!smsRequired && summary.success.length) {
                userInfoForSentSms = userInfoForSentSms.filter(({ _id }) => {
                    return !summary.success.includes(_id)
                })
            }
        }

        logger.info("🚀 ~ :224 ~ NotificationController ~ sentNotification ~ userInfoForSentSms:", userInfoForSentSms)

        if (userInfoForSentSms.length) {
            const smsResult = await Promise.allSettled(
                userInfoForSentSms.map(({ _id, user_id }) => {
                    let body = smsBody
                    const language = userSettingsMap[_id]

                    if (language === LANGUAGE.AR && secondaryLanguageSmsBody) {
                        body = secondaryLanguageSmsBody
                    }
                    return smsHandler.sendSMS(getMobileNumber(user_id), body)
                })
            )

            smsResult.forEach(result => {
                if (result.reason) {
                    logger.error(result.reason)
                }
            })
        }
    }

    async scheduleNotification(req, res) {
        const {
            tenantId,
            imageName,
            scheduleAt,
            timezone
        } = req.body

        try {
            req.body.scheduleAt = momentTimezone(scheduleAt).tz(timezone)

            await ScheduledNotificationModel.scheduleNotification(
                req.body,
                req.headers.userDetails._id
            )

            return res.handler.success()
        }
        catch (error) {
            if (imageName) {
                const publicS3 = new FileUpload(BUCKET_TYPE.PUBLIC);

                publicS3.deleteFile(
                    FILE_PATH.SCHEDULED_NOTIFICATION_IMAGES + "/" + tenantId,
                    imageName
                )
            }

            return res.handler.serverError(error)
        }
    }

    async updateScheduledNotification(req, res) {
        const publicS3 = new FileUpload(BUCKET_TYPE.PUBLIC);

        const {
            tenantId,
            title,
            message,
            imageName,
            scheduleAt,
            timezone
        } = req.body;

        try {
            const scheduledNotification = await ScheduledNotificationModel.getScheduledNotificationByFilter(req.body)
            if (!scheduledNotification)
                return res.handler.notFound("scheduled_notification_not_found")

            if (scheduledNotification.is_send)
                return res.handler.validationError("notification_scheduled")

            scheduledNotification.title = title
            scheduledNotification.message = message
            scheduledNotification.scheduleAt = momentTimezone(scheduleAt).tz(timezone)

            if (imageName) {
                if (scheduledNotification.image_name) {
                    publicS3.deleteFile(
                        FILE_PATH.SCHEDULED_NOTIFICATION_IMAGES + "/" + tenantId,
                        scheduledNotification.image_name
                    )
                }

                scheduledNotification.image_name = imageName
            }

            await ScheduledNotificationModel.save(
                scheduledNotification,
                req.headers.userDetails._id
            )

            return res.handler.success()
        }
        catch (error) {
            if (imageName) {
                publicS3.deleteFile(
                    FILE_PATH.SCHEDULED_NOTIFICATION_IMAGES + "/" + tenantId,
                    imageName
                )
            }

            return res.handler.serverError(error)
        }
    }

    async sentScheduledNotification() {
        var profiler = logger.startTimer()
        logger.info("Started: Sent scheduled notifications")

        try {
            const tenants = await SystemPortalModal.getTenants(
                {
                    is_active: true,
                    is_deleted: false,
                },
                "_id",
                {
                    "lean": true,
                }
            )
            const customerRole = await TenantPortalModal.getCustomerRole()

            const currentDate = new Date()

            for (let i = 0; i < tenants.length; i++) {
                try {
                    const tenantId = tenants[i]._id

                    const notificationFilter = {
                        tenant_id: tenantId,
                        is_send: false,
                        schedule_at: {
                            $gte: moment(currentDate).toDate(),
                            $lte: moment(currentDate).add(15, 'm').toDate(),
                        }
                    }

                    const scheduledNotifications = await ScheduledNotificationModel.listScheduledNotifications(notificationFilter)

                    for (let i = 0; i < scheduledNotifications.length; i++) {
                        try {
                            const scheduledNotification = scheduledNotifications[i]
                            const {
                                title,
                                message,
                                image_name,
                                notification_type,
                                price_list_ids,
                                data
                            } = scheduledNotification;

                            const notificationData = {
                                tenantId,
                                title,
                                message,
                                smsBody: message,
                                type: notification_type,
                                threadId: notification_type,
                                payloadData: {
                                    ...data,
                                    imageUrl: FILE_PATH.S3_URL.SCHEDULED_NOTIFICATION_IMAGES + "/" + tenantId + "/" + image_name,
                                    tenantId
                                },
                            }

                            const limit = 50

                            const customerFilter = {
                                tenant_id: tenantId,
                                is_deleted: false,
                                price_list_id: {
                                    $in: price_list_ids
                                }
                            }

                            const customersCount = await TenantPortalModal.getTenantUsersCount({
                                role_id: customerRole._id,
                                ...customerFilter
                            })

                            const customersTotalPages = Math.ceil(customersCount / limit)

                            for (let currentPage = 1; currentPage <= customersTotalPages; currentPage++) {
                                try {
                                    const customers = await TenantPortalModal.getTenantUsers(
                                        {
                                            role_id: customerRole._id,
                                            ...customerFilter
                                        },
                                        "_id",
                                        {
                                            lean: true,
                                            sort: {
                                                created_at: 1,
                                            },
                                            skip: limit * (currentPage - 1),
                                            limit
                                        }
                                    )

                                    await this.sentNotification({
                                        userRoleIds: customers.map(customer => {
                                            return customer._id.toString()
                                        }),
                                        ...notificationData
                                    })
                                }
                                catch (customerTaskError) {
                                    logger.error(customerTaskError)
                                }
                            }

                            const salesPersonFilter = {
                                tenant_id: tenantId,
                                default_master_price_id: {
                                    $in: price_list_ids
                                }
                            }

                            const salesPersonCount = await TenantPortalModal.getUserRoleSettingsCount(salesPersonFilter)

                            const salesPersonTotalPages = Math.ceil(salesPersonCount / limit)

                            for (let currentPage = 1; currentPage <= salesPersonTotalPages; currentPage++) {
                                try {
                                    const salesPersonSettings = await TenantPortalModal.findUserRoleSettings(
                                        salesPersonFilter,
                                        "_id",
                                        {
                                            lean: true,
                                            sort: {
                                                created_at: 1,
                                            },
                                            skip: limit * (currentPage - 1),
                                            limit
                                        }
                                    )

                                    await this.sentNotification({
                                        userRoleIds: salesPersonSettings.map(salesPersonSetting => {
                                            return salesPersonSetting._id.split("_")[1]
                                        }),
                                        ...notificationData
                                    })
                                }
                                catch (salesPersonTaskError) {
                                    logger.error(salesPersonTaskError)
                                }
                            }

                            scheduledNotification.is_send = true
                            await ScheduledNotificationModel.save(scheduledNotification)
                        }
                        catch (error) {
                            logger.error(error)
                        }
                    }
                }
                catch (error) {
                    logger.error(error)
                }
            }
        }
        catch (error) {
            logger.error(error)
        }
        finally {
            profiler.done({ message: "SENT SCHEDULED NOTIFICATION" })
            logger.info(`Completed: Sent scheduled notifications\n`)
        }
    }
}

module.exports = NotificationController 
