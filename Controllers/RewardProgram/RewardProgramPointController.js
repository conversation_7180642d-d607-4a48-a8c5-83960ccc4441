const RewardProgramMemberModel = new (require("../../Models/RewardProgram/RewardProgramMemberModel"))();
const RewardProgramPointsLogModel = new (require("../../Models/RewardProgram/RewardProgramPointsLogModel"))();
const RewardProgramMemberExpiryPointsModel = new (require("../../Models/RewardProgram/RewardProgramMemberExpiryPointsModel"))();
const RewardProgramModel = new (require("../../Models/RewardProgram/RewardProgramModel"))();
const RewardProgramNotificationModel = new (require("../../Models/RewardProgram/RewardProgramNotificationModel"))();

const CommonModel = new (require("../../Models/common"))();
const SAPServiceModel = new (require("../../Models/SAPServiceModel"))();

const RewardProgramController = new (require("./RewardProgramController"))()

const EncryptionHandler = new (require("../../Configs/encrypt"))();

const sendFailureEmailSAPServer = require("../../Middleware/SendFailureEmail").SAPServer

const {
    STATUS_CODES,
    SAP_SERVICE,
    REWARD_PROGRAM,
    ENTITY_STATUS,
    QR_CODE_TYPE
} = require('../../Configs/constants');

const {
    getQrCodeBase64,
} = require("../../Utils/helpers");

module.exports = class {

    listPointsLogs = async (req, res) => {
        try {
            const data = await RewardProgramPointsLogModel.findPointsLogsWithPagination(req.query)
            return res.handler.success(undefined, data)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    addDailyAccessPoints = async (req, res) => {
        try {
            const {
                tenantId,
                customerUserRoleId,
                timezone
            } = req.body

            const memberInfo = await RewardProgramMemberModel.findMemberByFilter(
                {
                    tenantId,
                    customerUserRoleId,
                    status: ENTITY_STATUS.ACTIVE
                },
                {
                    membership: 1,
                    reward_program_id: 1,
                    customer_user_role_id: 1,
                    coins: 1,
                    vip_points: 1,
                    coins_statistics: 1,
                    vip_points_statistics: 1,
                },
                {
                    populate: [
                        {
                            path: "reward_program_id",
                            select: "milestones classic_member_coin_rules vip_member_coin_rules"
                        },
                    ]
                }
            )

            if (!memberInfo)
                return res.handler.notFound("reward_program_member_not_found")

            const coinRuleKey = REWARD_PROGRAM.MEMBER.COIN_RULES_CONFIGURATION_KEY[memberInfo.membership]

            const logType = REWARD_PROGRAM.POINT.LOG_TYPE.ACTION_TYPE.DAILY_ACCESS
            const entryType = REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED
            const pointType = REWARD_PROGRAM.POINT.TYPE.COINS

            const todayDate = momentTimezone.tz(timezone)
            const startDate = todayDate.startOf("day").format();
            const endDate = todayDate.endOf("day").format();

            const pointsLogs = await RewardProgramPointsLogModel.findPointsLogsByFilter({
                tenant_id: tenantId,
                customer_user_role_id: customerUserRoleId,
                log_type: logType,
                entry_type: entryType,
                point_type: pointType,
                created_at: {
                    $gte: new Date(startDate),
                    $lte: new Date(endDate),
                },
            })

            if (pointsLogs.length)
                return res.handler.conflict("daily_app_access_points_limit_exceeded")

            const dailyAccessPoints = memberInfo.reward_program_id[coinRuleKey]?.action_types.daily_access
            if (!dailyAccessPoints)
                return res.handler.notFound("reward_program_configuration_not_found")

            await RewardProgramPointsLogModel.addPointsLog(
                {
                    rewardProgramId: memberInfo.reward_program_id._id,
                    rewardProgramMemberId: memberInfo._id,
                    customerUserRoleId: memberInfo.customer_user_role_id,
                    points: dailyAccessPoints,
                    tenantId,
                    logType,
                    entryType,
                    pointType,
                },
                memberInfo,
                req.headers.userDetails._id,
            )

            return res.handler.success(undefined, {
                points: dailyAccessPoints
            })
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    getQrCodeForMemberScan = async (req, res) => {
        try {
            const memberInfo = await RewardProgramMemberModel.findMemberByFilter(
                {
                    status: ENTITY_STATUS.ACTIVE,
                    ...req.query,
                },
                {
                    tenant_id: 1,
                    member_id: 1,
                    customer_user_role_id: 1
                }
            )

            if (!memberInfo)
                return res.handler.notFound("reward_program_member_not_found")

            const qrInfo = QR_CODE_TYPE.REWARD_PROGRAM_MEMBER
                + "_" + memberInfo.tenant_id
                + "_" + memberInfo.member_id
                + "_" + memberInfo.customer_user_role_id
                + "_" + new Date()

            const encryptedText = await EncryptionHandler.encrypt(qrInfo)
            const {
                status = "",
                data = "",
                error,
            } = encryptedText

            if (status === STATUS_CODES.SERVER_ERROR) {
                return res.handler.badRequest("unable_to_generate_qr_code", undefined, error)
            }

            const qrCode = await getQrCodeBase64(data)
            return res.handler.success(undefined, qrCode)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    scanQrCodeForMemberScan = async (req, res) => {
        try {
            const {
                tenantId,
                customerUserRoleId,
                qrInfo,
                timezone
            } = req.body

            const decryptedQr = EncryptionHandler.decrypt(qrInfo);
            const {
                status = "",
                data = "",
                error,
            } = decryptedQr || {};

            if (status === STATUS_CODES.SERVER_ERROR) {
                return res.handler.badRequest("unable_to_parse_qr", undefined, error)
            }

            const [
                type,
                tenant_id,
                member_id,
                customer_user_role_id,
                date
            ] = data.split("_")

            if (
                !type ||
                !tenant_id ||
                !member_id ||
                !customer_user_role_id ||
                !date
            ) {
                return res.handler.badRequest("invalid_reward_member_qr")
            }

            if (type !== QR_CODE_TYPE.REWARD_PROGRAM_MEMBER)
                return res.handler.badRequest("qr_code_mismatched_with_type")

            if (tenantId !== parseInt(tenant_id))
                return res.handler.badRequest("qr_code_mismatched_with_tenant")

            if (customerUserRoleId !== customer_user_role_id)
                return res.handler.badRequest("qr_code_mismatched_with_customer")


            const memberInfo = await RewardProgramMemberModel.findMemberByFilter(
                {
                    tenantId,
                    customerUserRoleId,
                    memberId: member_id,
                    status: ENTITY_STATUS.ACTIVE
                },
                {
                    membership: 1,
                    reward_program_id: 1,
                    customer_user_role_id: 1,
                    coins: 1,
                    vip_points: 1,
                    coins_statistics: 1,
                    vip_points_statistics: 1,
                },
                {
                    populate: [
                        {
                            path: "reward_program_id",
                            select: "milestones classic_member_coin_rules vip_member_coin_rules"
                        },
                    ]
                }
            )

            if (!memberInfo)
                return res.handler.notFound("reward_program_member_not_found")

            const coinRuleKey = REWARD_PROGRAM.MEMBER.COIN_RULES_CONFIGURATION_KEY[memberInfo.membership]

            const memberScanRule = memberInfo.reward_program_id[coinRuleKey]?.action_types.member_scan
            if (!memberScanRule)
                return res.handler.notFound("reward_program_configuration_not_found")

            const scanTimeDifference = new Date() - new Date(date)
            const scanTimeLimit = (memberScanRule.scan_duration_limit + 20) * 1000
            if (scanTimeDifference > scanTimeLimit) {
                return res.handler.badRequest("reward_member_qr_expired")
            }


            const logType = REWARD_PROGRAM.POINT.LOG_TYPE.ACTION_TYPE.MEMBER_SCAN
            const entryType = REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED
            const pointType = REWARD_PROGRAM.POINT.TYPE.COINS

            const todayDate = momentTimezone.tz(timezone)
            const startDate = todayDate.startOf("day").format();
            const endDate = todayDate.endOf("day").format();

            const pointsLogs = await RewardProgramPointsLogModel.findPointsLogsByFilter({
                tenant_id: tenantId,
                customer_user_role_id: customerUserRoleId,
                log_type: logType,
                entry_type: entryType,
                point_type: pointType,
                created_at: {
                    $gte: new Date(startDate),
                    $lte: new Date(endDate),
                },
            })

            if (pointsLogs.length)
                return res.handler.conflict("daily_member_scan_points_limit_exceeded")

            const pointLog = await RewardProgramPointsLogModel.addPointsLog(
                {
                    rewardProgramId: memberInfo.reward_program_id._id,
                    rewardProgramMemberId: memberInfo._id,
                    customerUserRoleId: memberInfo.customer_user_role_id,
                    points: memberScanRule.coins,
                    tenantId,
                    logType,
                    entryType,
                    pointType,
                },
                memberInfo,
                req.headers.userDetails._id,
            )
        
            return res.handler.success(undefined, {
                type: QR_CODE_TYPE.REWARD_PROGRAM_MEMBER
            })
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    addManualPoints = async (req, res) => {
        try {
            const {
                tenantId,
                customerUserRoleId,
                rewardProgramMemberId,
                logType,
            } = req.body

            const memberInfo = await RewardProgramMemberModel.findMemberByFilter(
                {
                    tenantId,
                    customerUserRoleId,
                    rewardProgramMemberId,
                    status: ENTITY_STATUS.ACTIVE
                },
                {
                    membership: 1,
                    coins: 1,
                    vip_points: 1,
                    coins_statistics: 1,
                    vip_points_statistics: 1,
                },
                {
                    populate: [
                        {
                            path: "reward_program_id",
                            select: "milestones"
                        },
                    ]
                }
            )

            if (!memberInfo)
                return res.handler.notFound("reward_program_member_not_found")

            await RewardProgramPointsLogModel.addPointsLog(
                {
                    logType: logType || REWARD_PROGRAM.POINT.LOG_TYPE.ACTION_TYPE.MANUAL,
                    entryType: REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED,
                    ...req.body,
                },
                memberInfo,
                req.headers.userDetails._id,
            )

            return res.handler.success()
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    calculateSAPPoints = async (req, res) => {
        try {
            const {
                paymentAmount,
                aging,
                reward
            } = req.body

            let remainingAmount = paymentAmount

            const calculations = [
                {
                    days: "91_120",
                    aging: aging.balance_91_120_days,
                    rewardPerSAR: reward.payment["91_120_days"] / reward.baseAmount,
                },
                {
                    days: "61_90",
                    aging: aging.balance_61_90_days,
                    rewardPerSAR: reward.payment["61_90_days"] / reward.baseAmount,
                },
                {
                    days: "31_60",
                    aging: aging.balance_31_60_days,
                    rewardPerSAR: reward.payment["31_60_days"] / reward.baseAmount,
                },
                {
                    days: "0_30",
                    aging: aging.balance_0_30_days,
                    rewardPerSAR: reward.payment["0_30_days"] / reward.baseAmount,
                }
            ]

            for (let i = 0; i < calculations.length; i++) {
                if (remainingAmount <= 0) {
                    break;
                }

                const calculation = calculations[i];

                if (
                    (calculations.length - 1) == i
                    || calculation.aging >= remainingAmount
                ) {
                    calculation.amountUsed = remainingAmount
                    calculation.earnPoints = remainingAmount * calculation.rewardPerSAR
                    calculation.remainingAmount = 0
                    break;
                }

                calculation.amountUsed = calculation.aging
                calculation.earnPoints = calculation.aging * calculation.rewardPerSAR

                remainingAmount = remainingAmount - calculation.aging
                calculation.remainingAmount = remainingAmount
            }

            const totalPointsEarn = calculations.reduce((sum, a) => sum + (a.earnPoints ?? 0), 0)

            return res.handler.success(undefined, {
                paymentAmount,
                totalPointsEarn,
                calculations
            })
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    checkSAPPoints = async (req, res) => {
        try {
            const {
                customerExternalId,
                date,
                membership,
                rewardProgramId,
                configurations
            } = req.query

            const rewardProgram = await RewardProgramModel.findRewardProgramById(
                rewardProgramId,
                {
                    _id: 0,
                    base_amount: 1,
                    classic_member_coin_rules: 1,
                    vip_member_coin_rules: 1,
                    vip_points_rules: 1,
                }
            )

            if (!rewardProgram)
                return res.handler.notFound("reward_program_configuration_not_found")

            const {
                points,
                errors,
                statementsSummeryWithDate,
                statementsInfo,
            } = await this.getPointCalculationsByCustomerInfo(
                customerExternalId,
                date,
                date,
                membership,
                rewardProgram,
                configurations
            )

            return res.handler.success(undefined, {
                points,
                errors,
                statementsSummeryWithDate,
                statements: statementsInfo?.statements ?? [],
                rewardProgram,
            })
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    addPointsThroughSAP = async () => {
        try {
            var profiler = logger.startTimer()
            logger.info("Started: Add reward points through SAP cron job")

            const date = parseInt(moment().subtract(1, "day").format("YYYYMMDD"))

            const addPointsByRewardProgram = async (
                rewardProgram,
                sapIntegrationCredentials,
            ) => {
                const rewardProgramMembersFilter = {
                    rewardProgramId: rewardProgram._id,
                    tenantId: rewardProgram.tenant_id,
                    status: ENTITY_STATUS.ACTIVE
                }

                await RewardProgramMemberModel.membersCallBackWithPagination(
                    rewardProgramMembersFilter,
                    this.addPointsByRewardMember(
                        date,
                        date,
                        rewardProgram,
                        sapIntegrationCredentials
                    )
                )
            }

            await RewardProgramController.rewardProgramsCallBackBasedTenants(addPointsByRewardProgram, false)
        }
        catch (error) {
            logger.error(error)
        }
        finally {
            profiler.done({ message: "ADD REWARD POINTS THROUGH SAP JOB RUN TIME" })
            logger.info(`Completed: Add reward points through SAP cron job\n`)
        }
    }

    addPointsByRewardMember = (
        fromDate,
        toDate,
        rewardProgram,
        sapIntegrationCredentials
    ) => async (memberInfo) => {
        const customerExternalId = memberInfo.customer_user_role_id.external_id

        const {
            points,
            errors,
        } = await this.getPointCalculationsByCustomerInfo(
            customerExternalId,
            fromDate,
            toDate,
            memberInfo.membership,
            rewardProgram,
            sapIntegrationCredentials
        )

        for (let i = 0; i < points.length; i++) {
            try {
                const point = points[i];

                await RewardProgramPointsLogModel.addPointsLog(
                    {
                        tenantId: rewardProgram.tenant_id,
                        customerUserRoleId: memberInfo.customer_user_role_id._id,
                        rewardProgramMemberId: memberInfo._id,
                        rewardProgramId: rewardProgram._id,
                        entryType: REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED,
                        pointType: point.point_type,
                        logType: point.log_type,
                        logDate: point.log_date,
                        calculations: point.calculations,
                        points: point.points,
                        amount: point.amount
                    },
                    memberInfo
                )
            }
            catch (error) {
                logger.error(error)
            }
        }

        if (errors.length) {
            await sendFailureEmailSAPServer(
                "Found some errors on statements from SAP",
                rewardProgram.tenant_id,
                {
                    customerExternalId,
                    errors
                }
            )
        }
    }

    expireRewardProgramMemberCoins = async () => {
        try {
            var profiler = logger.startTimer()
            logger.info("Started: Expire reward program member coins cron job")

            const expireDate = moment().subtract(1, "months")
            const year = parseInt(expireDate.format("YYYY"))
            const month = parseInt(expireDate.format("MM"))

            const expirePointsByRewardProgram = async (
                rewardProgram,
            ) => {
                const filter = {
                    reward_program_id: rewardProgram._id,
                    tenant_id: rewardProgram.tenant_id,
                    expiry_year: year,
                    expiry_month: month
                }

                const expirePointsOneByOne = async (expirePointInfo) => {
                    await CommonModel.transactionCallback(async (session) => {
                        const memberInfo = await RewardProgramMemberModel.findMemberById(
                            expirePointInfo.reward_program_member_id,
                            {
                                membership: 1,
                                coins: 1,
                                vip_points: 1,
                                coins_statistics: 1,
                                vip_points_statistics: 1,
                            },
                            {
                                session,
                                populate: [
                                    {
                                        path: "reward_program_id",
                                        select: "milestones"
                                    },
                                ]
                            }
                        )

                        const pointLog = await RewardProgramPointsLogModel.addPointsLog(
                            {
                                tenantId: rewardProgram.tenant_id,
                                customerUserRoleId: expirePointInfo.customer_user_role_id,
                                rewardProgramMemberId: expirePointInfo.reward_program_member_id,
                                rewardProgramId: rewardProgram._id,
                                pointType: REWARD_PROGRAM.POINT.TYPE.COINS,
                                entryType: REWARD_PROGRAM.POINT.ENTRY_TYPE.EXPIRED,
                                logType: REWARD_PROGRAM.POINT.LOG_TYPE.EXPIRED.COINS,
                                points: expirePointInfo.points,
                            },
                            memberInfo,
                            undefined,
                            session
                        )



                        await expirePointInfo.deleteOne({ session })
                    })
                }

                await RewardProgramMemberExpiryPointsModel.expiryPointsCallBackWithPagination(filter, expirePointsOneByOne)
            }

            await RewardProgramController.rewardProgramsCallBackBasedTenants(expirePointsByRewardProgram)
        }
        catch (error) {
            logger.error(error)
        }
        finally {
            profiler.done({ message: "EXPIRE REWARD PROGRAM MEMBER COINS" })
            logger.info(`Completed: Expire reward program member coins cron job\n`)
        }
    }

    getPointCalculationsByCustomerInfo = async (
        customerExternalId,
        fromDate,
        toDate,
        membership,
        rewardProgram,
        sapIntegrationCredentials
    ) => {
        let points = []
        let errors = []

        if (!customerExternalId) {
            return {
                points,
                errors,
            }
        }

        const statementsInfo = await SAPServiceModel.getStatements(
            {
                customerExternalId,
                fromDate,
                toDate,
            },
            sapIntegrationCredentials
        )

        if (!statementsInfo) {
            return {
                points,
                errors,
            }
        }

        const statementsSummeryWithDate = SAPServiceModel.generateStatementsSummery(statementsInfo.statements)
        const statementsDates = Object.keys(statementsSummeryWithDate)

        for (let i = 0; i < statementsDates.length; i++) {
            const statementsDate = statementsDates[i]
            const agingDate = parseInt(moment(statementsDate, "YYYY/MM/DD").subtract(1, "day").format("YYYYMMDD"))
            const statementsSummery = statementsSummeryWithDate[statementsDate]

            let aging

            if ((statementsSummery[SAP_SERVICE.DOCUMENT_TYPE.PAYMENT]?.credit_amount ?? 0) > 0) {
                aging = await SAPServiceModel.getBalance(
                    {
                        customerExternalId,
                        agingDate,
                    },
                    sapIntegrationCredentials
                )

                aging.date = agingDate
            }

            RewardProgramPointsLogModel.calculatePointsFromStatementSummery(
                points,
                errors,
                statementsSummery,
                statementsDate,
                aging,
                membership,
                rewardProgram
            )
        }

        return {
            points,
            errors,
            statementsInfo,
            statementsSummeryWithDate,
        }
    }

}
