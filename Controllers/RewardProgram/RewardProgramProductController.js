const RewardProgramMemberModel = new (require("../../Models/RewardProgram/RewardProgramMemberModel"))();
const RewardProgramPointsLogModel = new (require("../../Models/RewardProgram/RewardProgramPointsLogModel"))();
const RewardProgramProductModel = new (require("../../Models/RewardProgram/RewardProgramProductModel"))();
const RewardProgramProductTagModel = new (require("../../Models/RewardProgram/RewardProgramProductTagModel"))();
const RewardProgramProductClaimsModel = new (require("../../Models/RewardProgram/RewardProgramProductClaimsModel"))();
const SAPServiceModel = new (require("../../Models/SAPServiceModel"))();
const IntegrationCredentialModel = new (require("../../Models/IntegrationCredentialModel"))();
const CommonModel = new (require("../../Models/common"))();

const FileUpload = require('../../Configs/awsUploader').S3Upload;

const {
    BUCKET_TYPE,
    FILE_PATH,
    REWARD_PROGRAM,
    ENTITY_STATUS,
    STATUS_CODES,
    ENTITY_UPDATE_STATUS,
    INTEGRATION_CHANNELS
} = require('../../Configs/constants');

module.exports = class {

    addRewardProduct = async (req, res) => {
        const {
            tags,
            tenantId,
            itemNumber,
            productVariantId,
            productImageName,
            customImageName,
            name,
            secondaryLanguageName,
            requiredCoins,
            inventory,
            isFeatured,
            isActive,
        } = req.body

        const publicS3 = new FileUpload(BUCKET_TYPE.PUBLIC);

        const deleteCustomImage = async () => {
            if (!customImageName)
                return

            await publicS3.deleteFile(
                FILE_PATH.REWARD_PROGRAM_PRODUCTS_IMAGES + "/" + tenantId,
                customImageName
            )
        }

        try {
            await CommonModel.transactionCallback(async (session) => {
                const rewardExist = await RewardProgramProductModel.findProductsByItemNumber(
                    itemNumber,
                    tenantId,
                    undefined,
                    {
                        session
                    }
                )

                if (
                    rewardExist &&
                    (
                        !rewardExist.is_deleted ||
                        rewardExist.product_variant_id.toString() !== productVariantId
                    )
                ) {
                    await deleteCustomImage()
                    return res.handler.conflict("item_number_already_exists")
                }

                req.body.tags = await RewardProgramProductTagModel.getUpdatedTagsUsingRewardProductTags(
                    tags,
                    rewardExist?.tags,
                    tenantId,
                    session,
                    req.headers.userDetails._id,
                    rewardExist?.is_active === isActive ?
                        (
                            isActive ?
                                ENTITY_UPDATE_STATUS.NOT_CHANGED_ACTIVE :
                                ENTITY_UPDATE_STATUS.NOT_CHANGED_INACTIVE
                        ) :
                        (
                            isActive ?
                                ENTITY_UPDATE_STATUS.ACTIVE :
                                ENTITY_UPDATE_STATUS.INACTIVE
                        )
                )

                if (productImageName) {
                    await publicS3.copyImage(
                        FILE_PATH.PRODUCT_WEB_IMAGE + "/" + tenantId + "/" + productImageName,
                        FILE_PATH.REWARD_PROGRAM_PRODUCTS_IMAGES + "/" + tenantId + "/" + productImageName
                    )
                    req.body.imageName = productImageName
                }
                else if (customImageName) {
                    req.body.imageName = customImageName
                }

                if (rewardExist && rewardExist.is_deleted) {
                    if (
                        req.body.imageName &&
                        req.body.imageName !== rewardExist.imageName
                    ) {
                        await publicS3.deleteFile(
                            FILE_PATH.REWARD_PROGRAM_PRODUCTS_IMAGES + "/" + tenantId,
                            rewardExist.imageName
                        )
                    }

                    rewardExist.is_deleted = false
                    rewardExist.name = name
                    rewardExist.secondary_language_name = secondaryLanguageName
                    rewardExist.required_coins = requiredCoins
                    rewardExist.inventory = inventory
                    rewardExist.image_name = req.body.imageName
                    rewardExist.tags = req.body.tags

                    rewardExist.is_featured = isFeatured
                    rewardExist.is_active = isActive

                    await RewardProgramProductModel.save(
                        rewardExist,
                        req.headers.userDetails._id,
                        session
                    )
                } else {
                    await RewardProgramProductModel.addProduct(
                        req.body,
                        req.headers.userDetails._id,
                        session
                    )
                }

                return res.handler.success("reward_program_product_added")
            })
        }
        catch (error) {
            try {
                await deleteCustomImage()
            }
            catch (error) {
                logger.error(error)
            }
            finally {
                if (
                    error.code === STATUS_CODES.MONGODB_DUPLICATE_KEY_CODE &&
                    "unique_item_number" in error.keyValue
                ) {
                    return res.handler.conflict("item_number_already_exists")
                }
                return res.handler.serverError(error);
            }
        }
    }

    updateRewardProduct = async (req, res) => {
        const {
            tenantId,
            rewardProductId,
            name,
            secondaryLanguageName,
            requiredCoins,
            inventory,
            tags,
            productImageName,
            customImageName,
            isFeatured,
            isActive,
        } = req.body

        const publicS3 = new FileUpload(BUCKET_TYPE.PUBLIC);

        const deleteImage = async (imgName) => {
            if (!imgName)
                return

            await publicS3.deleteFile(
                FILE_PATH.REWARD_PROGRAM_PRODUCTS_IMAGES + "/" + tenantId,
                imgName
            )
        }

        try {
            await CommonModel.transactionCallback(async (session) => {
                const rewardInfo = await RewardProgramProductModel.findProductsById(
                    rewardProductId,
                    undefined,
                    {
                        session
                    }
                )

                if (!rewardInfo || rewardInfo?.is_deleted) {
                    await deleteImage(customImageName)
                    return res.handler.notFound("reward_program_product_not_found")
                }

                rewardInfo.tags = await RewardProgramProductTagModel.getUpdatedTagsUsingRewardProductTags(
                    tags,
                    rewardInfo.tags,
                    tenantId,
                    session,
                    req.headers.userDetails._id,
                    rewardInfo.is_active === isActive ?
                        (
                            isActive ?
                                ENTITY_UPDATE_STATUS.NOT_CHANGED_ACTIVE :
                                ENTITY_UPDATE_STATUS.NOT_CHANGED_INACTIVE
                        ) :
                        (
                            isActive ?
                                ENTITY_UPDATE_STATUS.ACTIVE :
                                ENTITY_UPDATE_STATUS.INACTIVE
                        )
                )

                if (productImageName) {
                    if (productImageName !== rewardInfo.imageName) {
                        await deleteImage(rewardInfo.imageName)
                    }

                    await publicS3.copyImage(
                        FILE_PATH.PRODUCT_WEB_IMAGE + "/" + tenantId + "/" + productImageName,
                        FILE_PATH.REWARD_PROGRAM_PRODUCTS_IMAGES + "/" + tenantId + "/" + productImageName
                    )

                    rewardInfo.image_name = productImageName
                }
                else if (customImageName) {
                    if (customImageName !== rewardInfo.imageName) {
                        await deleteImage(rewardInfo.imageName)
                    }

                    rewardInfo.image_name = customImageName
                }

                rewardInfo.name = name
                rewardInfo.secondary_language_name = secondaryLanguageName
                rewardInfo.required_coins = requiredCoins
                rewardInfo.inventory = inventory

                rewardInfo.is_featured = isFeatured
                rewardInfo.is_active = isActive

                await RewardProgramProductModel.save(
                    rewardInfo,
                    req.headers.userDetails._id,
                    session
                )

                return res.handler.success("reward_program_product_updated")
            })
        }
        catch (error) {
            try {
                await deleteImage(customImageName)
            }
            catch (error) {
                logger.error(error)
            }
            finally {
                return res.handler.serverError(error);
            }
        }
    }

    updateRewardProducts = async (req, res) => {
        try {
            await RewardProgramProductModel.updateProducts(
                req.body,
                {
                    is_active: req.body.isActive
                },
                req.headers.userDetails._id,
            )

            return res.handler.success("reward_program_products_updated")
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    listRewardProduct = async (req, res) => {
        try {
            const products = await RewardProgramProductModel.findProductsWithPagination(req.query)
            const reward_product_image_base_url = FILE_PATH.S3_URL.REWARD_PROGRAM_PRODUCTS_IMAGES + "/" + req.query.tenantId

            res.handler.success(undefined, {
                reward_product_image_base_url,
                ...products
            })
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    findRewardProducts = async (req, res) => {
        try {
            let { filter, projection, options } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const products = await RewardProgramProductModel.findProducts(
                filter,
                projection,
                options
            )

            return res.handler.success(null, products)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    deleteRewardProduct = async (req, res) => {
        try {
            await CommonModel.transactionCallback(async (session) => {
                const products = await RewardProgramProductModel.findProductsWithFilter(
                    req.query,
                    { tags: 1 },
                    { lean: true, session }
                );

                if (!products.length) {
                    return res.handler.notFound("reward_program_products_not_found")
                }

                const tagsSet = new Set(
                    products.flatMap(product => product.tags?.map(tag => tag.toString()) || [])
                );

                if (tagsSet.size) {
                    await RewardProgramProductTagModel.getUpdatedTagsUsingRewardProductTags(
                        [],
                        Array.from(tagsSet),
                        req.query.tenantId,
                        session,
                        req.headers.userDetails._id,
                        ENTITY_UPDATE_STATUS.INACTIVE
                    )
                }

                await RewardProgramProductModel.updateProducts(
                    req.query,
                    { is_deleted: true },
                    req.headers.userDetails._id,
                    session
                );

                return res.handler.success("reward_program_products_deleted");
            });

        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    listRewardProductTags = async (req, res) => {
        try {
            const tags = await RewardProgramProductTagModel.findTagsWithPagination(req.query)
            return res.handler.success(undefined, tags)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    claimRewardProduct = async (req, res) => {
        try {
            await CommonModel.transactionCallback(async (session) => {
                const {
                    tenantId,
                    customerUserRoleId,
                    rewardProgramId,
                    rewardProgramMemberId,
                    rewardProductId,
                } = req.body

                const memberInfo = await RewardProgramMemberModel.findMemberByFilter(
                    {
                        tenantId,
                        customerUserRoleId,
                        rewardProgramId,
                        memberId: rewardProgramMemberId,
                        status: ENTITY_STATUS.ACTIVE
                    },
                    {
                        membership: 1,
                        coins: 1,
                        vip_points: 1,
                        coins_statistics: 1,
                        vip_points_statistics: 1,
                    },
                    {
                        populate: [
                            {
                                path: "reward_program_id",
                                select: "milestones"
                            },
                            {
                                path: "customer_user_role_id",
                                select: "external_id"
                            },
                        ],
                        session
                    }
                )
                if (!memberInfo)
                    return res.handler.notFound("reward_program_member_not_found")

                const {
                    required_coins
                } = await RewardProgramProductModel.findProductsById(rewardProductId, "required_coins", {
                    session
                })

                if (memberInfo.coins.remaining < required_coins)
                    return res.handler.badRequest("reward_program_product_insufficient_coins")

                const sapIntegrationCredentials = await IntegrationCredentialModel.getCredential(
                    {
                        tenant_id: tenantId,
                        name: INTEGRATION_CHANNELS.SAP_SERVICE
                    },
                    "configurations is_active",
                    {
                        lean: true,
                        session
                    }
                )

                const aging = await SAPServiceModel.getBalance(
                    {
                        customerExternalId: memberInfo.customer_user_role_id.external_id,
                        agingDate: parseInt(moment().format("YYYYMMDD")),
                    },
                    sapIntegrationCredentials
                )

                if (aging) {
                    const dueAmount = aging.balance_91_120_days + aging.balance_120_days_above
                    if (dueAmount > 100) {
                        return res.handler.badRequest("reward_program_product_over_due_90_days", {
                            dueAmount
                        })
                    }
                }

                let newClaim

                newClaim = await RewardProgramProductClaimsModel.addClaim(
                    req.body,
                    required_coins,
                    req.headers.userDetails._id,
                    session
                )

                await RewardProgramPointsLogModel.addPointsLog(
                    {
                        tenantId,
                        customerUserRoleId,
                        rewardProgramMemberId,
                        rewardProgramId,
                        pointType: REWARD_PROGRAM.POINT.TYPE.COINS,
                        entryType: REWARD_PROGRAM.POINT.ENTRY_TYPE.CLAIMED,
                        logType: REWARD_PROGRAM.POINT.LOG_TYPE.REWARD.CLAIM,
                        productClaimId: newClaim._id,
                        points: required_coins,
                    },
                    memberInfo,
                    req.headers.userDetails._id,
                    session
                )

                return res.handler.success("reward_program_product_claimed")
            })
        }
        catch (error) {
            if (
                error.code === STATUS_CODES.MONGODB_DUPLICATE_KEY_CODE &&
                "unique_claim_number" in error.keyValue
            ) {
                return res.handler.conflict("claim_number_already_exists")
            }
            return res.handler.serverError(error);
        }
    }

    listRewardProductClaims = async (req, res) => {
        try {
            const claims = await RewardProgramProductClaimsModel.findClaimsWithPagination(req.query)
            return res.handler.success(undefined, claims)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    updateRewardProductClaimsStatus = async (req, res) => {
        try {
            const {
                tenantId,
                status,
                claimIds
            } = req.body

            await CommonModel.transactionCallback(async (session) => {
                const claimExists = await RewardProgramProductClaimsModel.findClaim(
                    {
                        tenantId,
                        claimIds,
                        status: [
                            REWARD_PROGRAM.REWARD.CLAIM.STATUS.CANCELLED,
                            REWARD_PROGRAM.REWARD.CLAIM.STATUS.DELIVERED,
                        ]
                    },
                    {
                        _id: 1,
                        status: 1
                    },
                    {
                        lean: true,
                        session
                    }
                )

                if (claimExists) {
                    if ([
                        REWARD_PROGRAM.REWARD.CLAIM.STATUS.DELIVERED,
                        REWARD_PROGRAM.REWARD.CLAIM.STATUS.CANCELLED,
                    ].includes(claimExists.status)) {
                        return res.handler.conflict(`${claimExists.status.toLowerCase()}_claim_product_exist`)
                    }
                }

                if (status === REWARD_PROGRAM.REWARD.CLAIM.STATUS.CANCELLED) {
                    const claims = await RewardProgramProductClaimsModel.findClaims(
                        {
                            tenantId,
                            claimIds,
                        },
                        {
                            customer_user_role_id: 1,
                            reward_program_id: 1,
                            claim_coins: 1,
                            reward_program_member_id: 1,
                        },
                        {
                            session,
                            lean: true
                        }
                    )

                    for (let i = 0; i < claims.length; i++) {
                        const claim = claims[i];

                        const memberInfo = await RewardProgramMemberModel.findMemberById(
                            claim.reward_program_member_id,
                            {
                                membership: 1,
                                coins: 1,
                                vip_points: 1,
                                coins_statistics: 1,
                                vip_points_statistics: 1,
                            },
                            {
                                session,
                                populate: [
                                    {
                                        path: "reward_program_id",
                                        select: "milestones"
                                    },
                                ]
                            }
                        )

                        await RewardProgramPointsLogModel.addPointsLog(
                            {
                                tenantId,
                                customerUserRoleId: claim.customer_user_role_id,
                                rewardProgramMemberId: claim.reward_program_member_id,
                                rewardProgramId: claim.reward_program_id,
                                productClaimId: claim._id,
                                pointType: REWARD_PROGRAM.POINT.TYPE.COINS,
                                entryType: REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED,
                                logType: REWARD_PROGRAM.POINT.LOG_TYPE.REWARD.REFUND,
                                points: claim.claim_coins,
                            },
                            memberInfo,
                            req.headers.userDetails._id,
                            session
                        )
                    }
                }

                await RewardProgramProductClaimsModel.updateClaims(
                    {
                        tenantId,
                        claimIds
                    },
                    {
                        status
                    },
                    req.headers.userDetails._id,
                    session
                )

                return res.handler.success("reward_program_product_claims_status_updated")
            })
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

}
