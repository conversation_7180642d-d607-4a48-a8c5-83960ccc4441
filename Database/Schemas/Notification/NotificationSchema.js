const Schema = mongoose.Schema;

const {
    NOTIFICATION
} = require('../../../Configs/constants');

const NotificationSchema = new Schema(
    {
        tenant_id: {
            type: Number,
        },
        user_role_id: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: 'user_roles',
            index: true
        },
        title: {
            type: String,
            trim: true,
            required: true
        },
        secondary_language_title: {
            type: String,
            trim: true,
        },
        message: {
            type: String,
            trim: true,
            required: true
        },
        secondary_language_message: {
            type: String,
            trim: true,
        },
        center_message: {
            type: String,
            trim: true,
            required: true
        },
        center_secondary_language_message: {
            type: String,
            trim: true,
            required: true
        },
        is_read: {
            type: Boolean,
            required: true,
            default: false
        },
        type: {
            type: String,
            required: true,
            trim: true,
            enum: Object.values(NOTIFICATION.TYPE),
        },
        thread_id: {
            type: String,
            trim: true,
        },
        payload_data: {                   
            type: Schema.Types.Mixed,
            required: true,
            /*
                Since Mixed is a schema-less type, you can change the value to anything else you like, but Mongoose loses the ability to auto detect and save those changes. To tell Mongoose that the value of a Mixed type has changed, you need to call doc.markModified(path)
                https://mongoosejs.com/docs/schematypes.html#mixed
            */
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
);

module.exports = mongoose.model('notifications', NotificationSchema);
