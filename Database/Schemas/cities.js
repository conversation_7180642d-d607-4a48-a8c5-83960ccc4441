const Schema = mongoose.Schema;

const CitySchema = new Schema({
    country_id: {
        type: Schema.Types.ObjectId,
        ref: "countries",
        required: true
    },
    region_id: {
        type: Schema.Types.ObjectId,
        ref: "regions",
        required: true
    },
    name: {
        type: String,
        required: true,
        trim: true,
    },
    secondary_language_name: {
        type: String,
        required: true,
        trim: true,
    },
    is_active: {
        type: Boolean,
        required: true
    },
    is_deleted: {
        type: Boolean,
        required: true
    },
    created_by: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true
    },
    updated_by: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model("cities", CitySchema);
