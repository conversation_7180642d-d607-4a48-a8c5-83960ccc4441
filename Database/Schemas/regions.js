const Schema = mongoose.Schema;

const RegionSchema = new Schema({
    country_id: {
        type: Schema.Types.ObjectId,
        ref: "countries",
        required: true,
    },
    name: {
        type: String,
        required: true,
        trim: true,
    },
    secondary_language_name: {
        type: String,
        required: true,
        trim: true,
    },
    code: {
        type: String,
        trim: true,
    },
    is_active: {
        type: Boolean,
        required: true
    },
    is_deleted: {
        type: Boolean,
        required: false,
    },
    secondary_language_code: {
        type: String,
        trim: true,
        // required: true
    },
    created_by: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true
    },
    updated_by: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model("regions", RegionSchema);
