const Schema = mongoose.Schema;

const { VALUES } = require('../../Configs/constants');

const RoleSchema = new Schema({
    name: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: true
    },
    portal_type: {
        type: String,
        required: true,
        enum: Object.values(VALUES.portals)
    },
    tenant_id: {
        type: Number
    },
    branch_id: {
        type: Schema.Types.ObjectId,
        ref: 'tenant_branches'
    },
    is_custom: {
        type: Boolean,
        required: true,
    },
    is_editable: {
        type: Boolean,
        // required: true,
    },
    is_deleteable: {
        type: Boolean,
        required: true,
    },
    permission: {                   // https://mongoosejs.com/docs/schematypes.html#mixed
        type: Schema.Types.Mixed,
        // Since Mixed is a schema-less type, you can change the value to anything else you like, but <PERSON><PERSON><PERSON> loses the ability to auto detect and save those changes. To tell Mongoose that the value of a Mixed type has changed, you need to call doc.markModified(path)
    },
    is_deleted: {
        type: <PERSON>olean,
        required: true,
    },
    is_active: {
        type: Boolean,
        required: true,
    },
    created_by: {
        type : Schema.Types.ObjectId,
        ref: 'users'
    },
    updated_by: {
        type : Schema.Types.ObjectId,
        ref: 'users'
    }

}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model('roles', RoleSchema);