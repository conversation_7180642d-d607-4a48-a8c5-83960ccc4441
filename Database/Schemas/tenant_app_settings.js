const Schema = mongoose.Schema;

const TenantAppSettingSchema = new Schema({
    tenant_id: {
        type: Number,
        required: true,
        ref: 'tenants',
    },
    quantity_label: {
        type: Number,
        required: true
    },
    consider_new_item: {
        type: Number,
        required: true
    },
    price_change: {
        type: <PERSON>olean,
        required: true
    },
    hide_out_of_stock_product: {
        type: Boolean,
        required: true
    },
    reduce_inventory: {
        type: Boolean,
        required: true
    },
    customer_app_access: {
        type: Boolean,
        required: true
    },
    catalog_mode: {
        type: Boolean,
        required: true
    },
    customer_auto_catalog_mode: {
        type: {
            enabled: {
                type: Boolean,
                required: true,
                default: false,
            },
            duration: {
                type: Number, // Duration in months
                required: true,
                default: 3,
            }
        }
    },
    preferred_language: {
        type: String,
        required: true
    },
    decimal_points: {
        type: Number,
        required: true,
        default: 0
    },
    payment_voucher_whatsapp_notification: {
        type: <PERSON>olean,
    },
    created_by: {
        type: Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    updated_by: {
        type: Schema.Types.ObjectId,
        ref: 'users',
        required: true
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
})

TenantAppSettingSchema.index({
    tenant_id: 1,
})

module.exports = mongoose.model("tenant_app_settings", TenantAppSettingSchema);