const { VALUES } = require('../../Configs/constants');
const Schema = mongoose.Schema;

const UserDeviceSchema = new Schema({
    user_id: {
        type: Schema.Types.ObjectId,
        required: true,
        // ref: 'users' it can refer to users collection or tenant_customers collections
    },
    device_token: {
        type: String,
        required: true
    },
    otp: {
        type: String,
    },
    device_verified: {
        type: Boolean,
        default: false
    },
},{
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model('user_devices', UserDeviceSchema);