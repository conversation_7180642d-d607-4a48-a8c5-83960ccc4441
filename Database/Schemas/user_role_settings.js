const mongoose = require("mongoose");

const Schema = mongoose.Schema;

//if no settings found for tenant_id & user_role_id combination, fallback to default values
const UserRolesSettingSchema = new Schema({
    _id: { // tenantId_userRoleId (1119_634015e7a2a6e700126c5638)
        type: String,
        required: true
    },
    tenant_id: {
        type: Number,
        required: true
    },
    user_role_id: {
        type: mongoose.Types.ObjectId,
        required: true
    },
    default_master_price_id: {
        type: Schema.Types.ObjectId
    },
    out_of_stock: {
        visible: {
            type: Boolean,
            required: true,
            default: true
        },
        searchable: {
            type: Boolean,
            required: true,
            default: false
        },
    },
    price_change: {
        type: Boolean,
        required: true,
        default: true
    },
    preferred_language: {
        type: String,
        trim: true,
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model('user_role_settings', UserRolesSettingSchema);