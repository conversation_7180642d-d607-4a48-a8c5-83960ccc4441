const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const { VALUES, PROFILE_TYPE_ENUM } = require('../../Configs/constants');
const { ShippingAddressSchema, GpsCoordinates } = require("./common_schemas");

const NotificationSettingSchema = new Schema({
    notification_type: {
        type: String,
        required: true,
        trim: true,
    },
    allow_push_notification: {
        type: Boolean,
        required: true,
        default: false
    },
    allow_sms_notification: {
        type: Boolean,
        required: true,
        default: false
    },
    allow_email_notification: {
        type: Boolean,
        required: true,
        default: false
    },
}, { _id: false });

const DeviceAccess = new Schema({
    device_id: {
        type: String,
        required: true,
        trim: true,
    },
    type: {
        type: String,
        required: true,
        enum: Object.values(VALUES.deviceAccessType)
    },
    os: {
        type: String,
        required: true,
        enum: Object.values(VALUES.deviceAccessOs)
    }
}, { _id: false });

// salesperson settings
const userSettingSchema = new Schema({
    default_master_price_id: {
        type: Schema.Types.ObjectId
    },
    out_of_stock: {
        visible: {
            type: Boolean,
            required: true,
            default: true
        },
        searchable: {
            type: Boolean,
            required: true,
            default: false
        },
    },
    price_change: {
        type: Boolean,
        required: true,
        default: true
    },
    preferred_language: {
        type: String,
        trim: true,
    }
}, { _id: false })

const UserRoleSchema = new Schema({
    role_id: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: 'roles',
        index: true
    },
    assigned_tenants: { // only consider this is role_id is account manager
        type: [Number], // array of tenant ids
        ref: "tenants",
        default: undefined
    },
    user_id: { // the reference is based on value of collection_name property. 
        // NOTE if you want to populate this field, do not forget to include collection_name when querying on user_roles collection

        type: Schema.Types.ObjectId,
        required: true,
        refPath: 'collection_name',
        index: true
    },
    collection_name: { // this will determine, wether user_id needs to be populated from "users" collection or "tenant_customers" collection
        type: String,
        required: true,
        enum: ["users", "tenant_customers"],
        default: "users"
    },
    tenant_id: { // this profile belongs to this tenant
        type: Number,
        ref: 'tenants'
    },
    branch_id: { // this profile belongs to this tenant
        type: Schema.Types.ObjectId,
        ref: "tenant_branches",
    },
    is_deleted: { // mark the profile as deleted.
        // NOTE: when we try to add a profile with the user_id, role_id & tenant_id; which already exist but marked as deleted, in this scenario we will restore the user
        type: Boolean,
        required: true,
        default: false
    },
    is_active: { // save the active/inactive status of profile
        type: Boolean,
        required: true,
        default: true
    },
    created_by: { // from which user this profile was created
        type: Schema.Types.ObjectId,
        ref: 'users'
    },
    updated_by: { // from which user this profile was last updated
        type: Schema.Types.ObjectId,
        ref: 'users',
    },
    allow_all_permission: { // for Tenant Owner, Admin & Supervisor role it will be true ( please refer {{base_url}}/tenant-portal/user, METHOD: POST)
        type: Boolean,
        required: false,
        default: false
    },
    allow_price_change: {
        // to store the status, wether the salesperson can change the price of the cart items. and this will be set from web portal, by Owner or Admin of the tenant
        type: Boolean,
    },
    allow_posting_voucher_receipts: {
        //Allow posting of voucher receipts option to admin profile. Only owner can give this permission
        type: Boolean,
    },
    notifications: {
        // this will store the notification settings for the given profile
        type: [NotificationSettingSchema],
    },
    supervisor_id: {
        // this will store the user_id of the supervisor assigned to the salesperson. ( this is not the user_roles collection's primary key )
        type: Schema.Types.ObjectId,
        ref: 'users',
    },
    // ====================== CUSTOMER FIELDS STARTS ====================== //
    sales_person_id: {
        // this will store the user_roles collection's _id of the salesperson profile assigned to the customer.
        type: Schema.Types.ObjectId,
        ref: 'user_roles',
    },
    customer_id: {
        // this is auto generated `${tenant_id}{auto increment from 1000}` ( for searching customer )
        type: String,
        trim: true,
        unique: true,
        sparse: true,
    },
    customer_first_name: {
        // this is the name provided to the profile when created customer, to store the different value of this property for different tenants
        type: String,
        trim: true,
    },
    customer_last_name: {
        // this is the name provided to the profile when created customer, to store the different value of this property for different tenants
        type: String,
        trim: true,
    },
    customer_email: {
        // this is the email provided to the profile when created customer, to store the different value of this property for different tenants
        type: String,
        trim: true,
    },
    customer_app_access: {
        // wether the customer has given access to mobile/tablet app from Owner or Admin
        type: Boolean,
    },
    customer_catalog_mode: {
        // wether the customer has given access to catalog mode from Owner or Admin
        type: Boolean,
    },
    customer_catalog_mode_enabled_at: {
        // when the customer catalog mode has turned true
        type: Date,
    },
    customer_catalog_mode_disabled_at: {
        // when the customer catalog mode has turned false
        type: Date,
    },
    customer_app_request: {
        // wether the customer app request is raised by his/her salesperson
        type: Boolean,
    },
    preferred_language: {
        // customer's preferred_language
        type: String,
        trim: true,
    },
    external_id: {
        // external id of the customer
        type: String,
        trim: true,
    },
    unique_external_id: {
        // to support the constraint of the external_id of customer
        type: String,
        trim: true,
        index: {
            partialFilterExpression: {
                unique_external_id: {
                    $exists: true,
                    $gt: "",
                },
                external_id: {
                    $exists: true,
                    $gt: "",
                }
            }
        },
        unique: true,
    },
    customer_name: {
        // this is the customer's name provided to the profile when created customer, to store the different value of this property for different tenants
        type: String,
        trim: true,
    },
    customer_legal_name: {
        // this is the customer's legal name provided to the profile when created customer, to store the different value of this property for different tenants
        type: String,
        trim: true,
    },
    customer_payment_term_info: {
        // this will store payment term of the customer user in reward program
        type: {
            number_of_days: {
                type: Number,
                required: true,
            },
        }
    },
    // portal_shipping_address: {
    //     type: ShippingAddressSchema
    // },
    // tablet_shipping_address: {
    //     type: ShippingAddressSchema
    // },
    shipping_address: {
        // to store the street address value of the customer
        type: String,
        trim: true,
    },
    shipping_country_id: {
        // reference to the country of the customer
        type: Schema.Types.ObjectId,
        ref: 'countries',
    },
    shipping_city_id: {
        // reference to the city of the customer
        type: Schema.Types.ObjectId,
        ref: 'cities',
    },
    shipping_region_id: {
        // reference to the region of the customer
        type: Schema.Types.ObjectId,
        ref: 'regions',
    },
    shipping_country_code: {
        // shipping mobile number's country code
        type: String,
        trim: true,
    },
    shipping_mobile_number: {
        type: Number
    },
    gps_coordinates: {
        // gps coordinated of the shipping address of the customer,
        // NOTE: if any property of shipping details of customer is missing, do not let place the order for the customer
        type: GpsCoordinates
    },
    device_access: {
        // this will manage the customer's login device details
        type: [DeviceAccess],
        default: undefined
    },
    price_list_id: {
        // what type of product's price will be applied for this customer
        type: Schema.Types.ObjectId,
        ref: 'tenant_pricelists',
    },
    // catalog_mode: {
    //     type: Boolean
    // },
    // ====================== CUSTOMER FIELDS ENDS ====================== //
    total_orders: {
        // number of overall total orders placed for this profile ( for salesperson or customer )
        type: Number
    },
    total_sales: {
        // number of overall total orders value for this profile ( for salesperson or customer )
        type: Number
    },
    is_verified: {
        // flag will determine wether the customer is verified or not
        type: Boolean
    },
    // // salesperson settings
    // settings: {
    // this is moved to user_roles_settings ( as we need to store settings for all tenant profiles)
    //     type: userSettingSchema
    // },

    profile_type: {
        // this is to determine wether this profile's user_id is used in deferent tenant or not
        type: String,
        enum: Object.values(PROFILE_TYPE_ENUM)
    },
    last_mobile_login_time: {
        // to support the latest logged-in session for the profile from mobile application
        type: Date
    },
    first_mobile_login_time: {
        // to support the first logged-in session for the profile from mobile application
        type: Date
    },
    last_tablet_login_time: {
        // to support the latest logged-in session for the profile from tablet application
        type: Date
    },
    profile_pic: {
        type: String
    },
    last_mobile_device_token: { // FOR now we will store this for Salesperson profile only. to check the current mobile device
        type: String
    },
    is_payment_enabled: {
        type: Boolean,
        default: false
    },
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});
UserRoleSchema.virtual('salesPersonDetail', {
    ref: "user_roles",
    localField: "sales_person_id",
    foreignField: "_id",
    justOne: true,

});

UserRoleSchema.set('toJSON', { virtuals: true })

/**
 * @description Followed ESR rule for below compound index
 * @reference https://www.mongodb.com/docs/manual/tutorial/equality-sort-range-rule/#std-label-esr-indexing-rule
 */
UserRoleSchema.index({
    tenant_id: 1,
    is_deleted: 1,
    is_active: 1,
    role_id: 1,
    user_id: 1,
    sales_person_id: 1,
    external_id: 1,
})

module.exports = mongoose.model('user_roles', UserRoleSchema);

// Collection watch
require("./change_streams/UserRolesChangeStream")
