{"info": {"_postman_id": "7320b4c1-c648-4676-95b9-83e3acf9b2ed", "name": "Hawak-User-Backend", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json", "_exporter_id": "30141118", "_collection_link": "https://lunar-flare-246963.postman.co/workspace/Hawak-Backend-Devs~838d78ef-c166-4c7b-9ee7-d04bc82e657e/collection/23115891-7320b4c1-c648-4676-95b9-83e3acf9b2ed?action=share&source=collection_link&creator=30141118"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Token Info", "item": [{"name": "Get token info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{useRoleId}}", "type": "text"}], "url": "{{base_url}}/auth/token-validity"}, "response": []}, {"name": "Update token validity", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"time\": 5,\n    \"timeUnit\": \"hours\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/auth/token-validity"}, "response": []}]}, {"name": "App <PERSON>", "item": [{"name": "<PERSON>pp <PERSON>gin", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Set app_session\", function () {", "    var jsonData = pm.response.json();", "    pm.collectionVariables.set(\"app_session\", jsonData.data.Session);", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "deviceaccesstype", "value": "{{deviceaccesstype}}", "description": "TABLET / MOBILE", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "devicetype", "value": "{{devicetype}}", "description": "IOS / ANDROID", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"countryCode\": \"+91\",\n    \"mobileNumber\": {{mobileNumber}}\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/auth/app-sign-in"}, "response": []}, {"name": "Verify the OTP", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set Auth Token\", function () {", "    var jsonData = pm.response.json();", "    pm.globals.set(\"Authorization\", jsonData.data.tokens.accessToken);", "    pm.globals.set(\"refreshToken\", jsonData.data.tokens.refreshToken);", "    pm.globals.set(\"userRoleId\", jsonData.data.userRoles[0]._id);", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "devicetype", "value": "{{devicetype}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{deviceaccesstype}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"session\": \"{{app_session}}\",\n    \"otp\": \"9815\",\n    \"mobileNumber\": {{mobileNumber}},\n    \"countryCode\": \"+91\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/auth/verify-auth-otp"}, "response": []}, {"name": "Get app user roles", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "deviceaccesstype", "value": "TABLET", "type": "text"}, {"key": "devicetype", "value": "IOS", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"countryCode\": \"+966\",\n    \"mobileNumber\": {{mobileNumber}}\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/app-user-roles", "host": ["{{base_url}}"], "path": ["auth", "app-user-roles"], "query": [{"key": "userId", "value": "62e275ac0b77a6bbdf16fac8", "disabled": true}]}}, "response": []}, {"name": "App resend otp", "request": {"method": "POST", "header": [{"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "devicetype", "value": "IOS", "type": "text"}, {"key": "deviceaccesstype", "value": "TABLET", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"session\": \"{{app_session}}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/auth/app-resend-otp"}, "response": []}]}, {"name": "Forgot Password/Reset Password", "item": [{"name": "Forgot password", "request": {"method": "POST", "header": [{"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "userRoleId", "value": "{{userRoleId}}", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"sendOtpTo\": \"MOBILE\",\n    \"mobileNumber\": \"90332777923\",\n    \"countryCode\": \"+91\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/forgot-password", "host": ["{{base_url}}"], "path": ["auth", "forgot-password"], "query": [{"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "disabled": true}]}}, "response": []}, {"name": "Verify forgot password with Change password", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"token\": \"$2a$10$uxXEVWeFqlEf5B/tc4x.eO5y.H1K6Mqxo31E5uLU3gJQGDo5NYUUW\",\n    \"id\": \"637cc5c88ef14a0012cdc62c\",\n    \"newPassword\": \"Test@1234\",\n    \"confirmPassword\": \"Test@1234\",\n    \"type\": \"EMAIL\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/auth/change-password-with-token"}, "response": []}, {"name": "Verify mobile otp for forgot password", "request": {"method": "POST", "header": [{"key": "", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"token\": \"2886\",\n    \"mobileNumber\": \"+918460353552\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/auth/verify-mobile-forgot-password-otp"}, "response": []}, {"name": "Check rest password token", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"token\": \"$2a$10$.Nb1cEV5ZSlX3rBmIliUtuLDmedJmocAikrII./megkNKGt0kB8HS\",\n    \"id\": \"62e275ac0b77a6bbdf16fac8\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/check-rest-password-link", "host": ["{{base_url}}"], "path": ["auth", "check-rest-password-link"], "query": [{"key": "userRoleId", "value": "{{userRoleId}}", "disabled": true}]}}, "response": []}]}, {"name": "Verify user", "request": {"method": "POST", "header": [{"key": "deviceToken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"confirmationCode\": \"6244\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/auth/verify-user"}, "response": []}, {"name": "Sign in", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set Auth Token\", function () {", "    var jsonData = pm.response.json();", "    pm.globals.set(\"Authorization\", jsonData.data.tokens.accessToken);", "    pm.globals.set(\"refreshToken\", jsonData.data.tokens.refreshToken);", "    pm.globals.set(\"userRoleId\", jsonData.data.userRoles[0]._id);", "});"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disabledSystemHeaders": {"user-agent": true, "accept-encoding": true, "connection": true}}, "request": {"method": "POST", "header": [{"key": "devicetype", "value": "{{devicetype}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{email}}\",\n    \"password\": \"{{password}}\",\n    \"type\": \"EMAIL\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/auth/sign-in"}, "response": []}, {"name": "Resend verification otp", "protocolProfileBehavior": {"strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"sendOtpTo\": \"EMAIL\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/auth/resend-otp"}, "response": []}, {"name": "Role accessed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "devicetype", "value": "{{devicetype}}", "description": "iOS or ANDROID", "type": "text"}, {"key": "deviceaccesstype", "value": "{{deviceaccesstype}}", "description": "TABLET or MOBILE", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"userRoleId\": \"63d7c3ec099872001245c559\",\n    \"tenantId\": {{tenantId}},\n    \"fcmToken\": \"token\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/auth/user-role-accessed"}, "response": []}, {"name": "Get user roles(profiles)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "deviceType", "value": "{{devicetype}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{deviceaccesstype}}", "type": "text"}], "url": {"raw": "{{base_url}}/auth/user-roles?userId=6368f0ee3b253000115866a6", "host": ["{{base_url}}"], "path": ["auth", "user-roles"], "query": [{"key": "userId", "value": "6368f0ee3b253000115866a6"}]}}, "response": []}, {"name": "User logout", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "url": {"raw": "{{base_url}}/auth/logout?userRoleId={{userRoleId}}", "host": ["{{base_url}}"], "path": ["auth", "logout"], "query": [{"key": "userRoleId", "value": "{{userRoleId}}"}]}}, "response": []}, {"name": "Service Authenticator", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": "{{base_url}}/auth/service-authenticator"}, "response": []}, {"name": "Generate Map image from lat long", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/generateMapFile?lat=0.0&lng=0.0", "host": ["{{base_url}}"], "path": ["auth", "generateMapFile"], "query": [{"key": "lat", "value": "0.0"}, {"key": "lng", "value": "0.0"}]}}, "response": []}, {"name": "G Map image generation", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/generateMapFile?lat=0.0&lng=0.0", "host": ["{{base_url}}"], "path": ["auth", "generateMapFile"], "query": [{"key": "lat", "value": "0.0"}, {"key": "lng", "value": "0.0"}]}}, "response": []}]}, {"name": "Common", "item": [{"name": "Countries", "item": [{"name": "Add country", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"countryName\": \"india\",\n    \"countryCode\": \"+91\",\n    \"timeZone\": \"Asia/Kolkata\",\n    \"mobileNumberFormat\": 10,\n    \"currency\": \"₹\",\n    \"isActive\" : true,\n    \"vat\": 0.0,\n    \"alphaTwoCode\": \"IN\",\n    \"secondaryLanguage\": \"hi\",\n    \"secondaryLanguageName\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/common/country"}, "response": []}, {"name": "Get countries", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/common/get-countries?type=PAGINATION&searchKey=ind", "host": ["{{base_url}}"], "path": ["common", "get-countries"], "query": [{"key": "type", "value": "PAGINATION", "description": "ALL  /  PAGINATION  /  SEARCH"}, {"key": "search<PERSON>ey", "value": "ind"}, {"key": "page", "value": "1", "disabled": true}, {"key": "perPage", "value": "20", "disabled": true}]}}, "response": []}, {"name": "edit country", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"countryId\": \"62e7dacf077fb60012eb35e6\",\n    \"countryName\": \"United Arab Emirates\",\n    \"countryCode\": \"+971\",\n    \"timeZone\": \"Asia/Dubai\",\n    \"mobileNumberFormat\": 10,\n    \"currency\": \"د.إ\",\n    \"isActive\" : true,\n    \"vat\": 1.2,\n    \"alphaTwoCode\": \"UAE\",\n    \"secondaryLanguage\": \"ar\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/common/country"}, "response": []}, {"name": "delete country", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/common/country?countryId=62e7d842077fb60012eb35e3", "host": ["{{base_url}}"], "path": ["common", "country"], "query": [{"key": "countryId", "value": "62e7d842077fb60012eb35e3"}]}}, "response": []}, {"name": "change country status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"countryId\": \"62e7dacf077fb60012eb35e6\",\n    \"status\": \"INACTIVE\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/common/country-status", "host": ["{{base_url}}"], "path": ["common", "country-status"], "query": [{"key": "", "value": "", "disabled": true}]}}, "response": []}]}, {"name": "User", "item": [{"name": "Update user password", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"type\": \"EMAIL_LINK\",\n    \"userId\": \"62e3de95d533d300122ad48f\",\n    \"password\": \"Test@1234\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/common/change-user-password"}, "response": []}, {"name": "Search users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/common/search-users?type=MOBILE&mobileNumber=6352&countryCode=%2B91", "host": ["{{base_url}}"], "path": ["common", "search-users"], "query": [{"key": "type", "value": "MOBILE"}, {"key": "mobileNumber", "value": "6352"}, {"key": "countryCode", "value": "%2B91"}]}}, "response": []}, {"name": "Chnage user profile pic", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "userId", "value": "6368f0ee3b253000115866a6", "type": "text"}, {"key": "tenantId", "value": "1131", "type": "text"}, {"key": "profilePic", "description": "if you want to delete profile pic then do not send it in body", "type": "file", "src": "/home/<USER>/Pictures/Regency.svg"}, {"key": "type", "value": "SHIPPING_LABEL", "type": "text", "disabled": true}, {"key": "file", "description": "if you want to delete file then do not send it in body", "type": "file", "src": [], "disabled": true}, {"key": "fileType", "value": "image/png", "type": "text", "disabled": true}]}, "url": "{{base_url}}/common/user-profile-pic"}, "response": []}]}, {"name": "City", "item": [{"name": "Chnage Status of Cities", "item": [{"name": "edit stauts od cities", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"cities\": [\n        \"63202567f201ef00122bfa43\"\n    ],\n    \"status\": \"ACTIVE\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/common/cities-status"}, "response": []}, {"name": "delete cities", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"cities\": [\n        \"63202567f201ef00122bfa43\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/common/cities-status"}, "response": []}]}, {"name": "Add City", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"regionId\": \"6303c222acf99987139a7275\",\n    \"isActive\": true,\n    \"name\": \"Ahmedabad\",\n    \"countryId\": \"62e7d842077fb60012eb35e3\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/common/city"}, "response": []}, {"name": "Edit city", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"regionId\": \"631ae5355eece300129f92da\",\n    \"isActive\": true,\n    \"name\": \"Rajkot\",\n    \"countryId\": \"62e7d842077fb60012eb35e3\",\n    \"cityId\": \"631ef726f540f900121c38b9\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/common/city"}, "response": []}, {"name": "City listing", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/common/city?type=PAGINATION&searchKey=", "host": ["{{base_url}}"], "path": ["common", "city"], "query": [{"key": "page", "value": "1", "disabled": true}, {"key": "perPage", "value": "10", "disabled": true}, {"key": "type", "value": "PAGINATION", "description": "ALL / SEARCH / PAGINATION"}, {"key": "search<PERSON>ey", "value": ""}, {"key": "status", "value": "ACTIVE", "disabled": true}, {"key": "cityIds[]", "value": "631f0b05a14bda0011082db9", "disabled": true}, {"key": "projections[]", "value": "name", "disabled": true}]}}, "response": []}, {"name": "Automatic add city ( mobile app )", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"countryName\": \"India\",\n    \"regionName\": \"kaRnataka\",\n    \"cityName\": \"Mysore\",\n    \"regionCode\": \"PIO\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/common/automatic-add-city"}, "response": []}]}, {"name": "Languages", "item": [{"name": "get languages", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/common/language?page=1&perPage=10&type=PAGINATION", "host": ["{{base_url}}"], "path": ["common", "language"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "type", "value": "PAGINATION"}]}}, "response": []}]}, {"name": "Regions", "item": [{"name": "add region", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"countryId\": \"62e7d842077fb60012eb35e3\",\n    \"isActive\": true,\n    \"name\": \"Madhya Pradesh\",\n    \"code\": \"MP\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/common/region"}, "response": []}, {"name": "edit region", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"code\": \"TRY\",\n    \"countryId\": \"6319ca0f728e4b0012c04ff3\",\n    \"isActive\": true,\n    \"name\": \"TYR\",\n    \"regionId\": \"631adc6acd06f700114b7c91\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/common/region"}, "response": []}, {"name": "Get Region", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/common/region?type=PAGINATION&page=1&perPage=10&countryId=62e7d842077fb60012eb35e3&status=ACTIVE&searchKey=ind", "host": ["{{base_url}}"], "path": ["common", "region"], "query": [{"key": "type", "value": "PAGINATION", "description": "ALL  /  SEARCH  /  PAGINATION"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "countryId", "value": "62e7d842077fb60012eb35e3"}, {"key": "status", "value": "ACTIVE"}, {"key": "search<PERSON>ey", "value": "ind"}]}}, "response": []}, {"name": "Delete Region", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/common/region?regionId=6303c222acf99987139a7275", "host": ["{{base_url}}"], "path": ["common", "region"], "query": [{"key": "regionId", "value": "6303c222acf99987139a7275"}]}}, "response": []}, {"name": "change region status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"regionId\": \"\",\n    \"status\": \"INACTIVE\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/common/region-status"}, "response": []}]}, {"name": "Image", "item": [{"name": "Add Image", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"imageName\": \"64c892d6d2e6f000120b590d.jpg\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/common/addImage"}, "response": []}, {"name": "Delete Image", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"imageName\": \"64c892d6d2e6f000120b590d.jpg\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/common/deleteImage"}, "response": []}]}, {"name": "Get Upload Signature", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1241,\n    \"dataType\": \"PRODUCT\",\n    \"operationType\": \"UPDATE\",\n    \"updateType\": \"DETAILS\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/common/getUploadSignature"}, "response": []}]}, {"name": "Integration Credentials", "item": [{"name": "Create Integration Credentials", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1241,\n    \"name\": \"SAP_SERVICE\",\n    \"configurations\": {\n        \"baseUrl\": \"https://www.google.com/\",\n        \"username\": \"admin\",\n        \"password\": \"admin\"\n    },\n    \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/integrationCredential"}, "response": []}, {"name": "Get Integration Credentials", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/integrationCredential?tenantId=1241", "host": ["{{base_url}}"], "path": ["integrationCredential"], "query": [{"key": "tenantId", "value": "1241"}, {"key": "names[]", "value": "SAP_SERVICE", "disabled": true}, {"key": "names[]", "value": "MESSAGE_BIRD", "disabled": true}]}}, "response": []}, {"name": "Update Integration Credentials", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1241,\n    \"name\": \"SAP_SERVICE\",\n    \"configurations\": {\n        \"baseUrl\": \"https://c20182as01p01.cloudiax.com:8443/B1iXcellerator/exec/ipo/vP.001sap0000.in_HCSX/com.sap.b1i.vplatform.runtime/INB_HT_CALL_SYNC_XPT/INB_HT_CALL_SYNC_XPT.ipo/proc/report\",\n        \"username\": \"<PERSON><PERSON><PERSON>\",\n        \"password\": \"12345678\"\n    },\n    \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/integrationCredential"}, "response": []}]}, {"name": "Languages", "item": [{"name": "Key", "item": [{"name": "Add Key API", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"key\": \"Testing_test_key1\",\n    \"type\": \"both\",\n    \"web\": \"\",\n    \"tab\": \"\",\n    \"both\": \"Testing_test_key\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/language/key"}, "response": []}, {"name": "Edit key api", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshtoken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"key\": \"VALIDATION.EXISTS.LANGUAGE_CODE###\",\n    \"type\": \"web-tab\",\n    \"web\": \"Popo\",\n    \"tab\": \"Popo\",\n    \"both\": \"\",\n    \"hasTypeChanged\": false,\n    \"languageCode\": \"chi\",\n    \"languageId\": \"6369e1b60edf4ceb319129ce\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/language/key"}, "response": []}]}, {"name": "Add language", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "translationFile", "type": "file", "src": "/home/<USER>/Downloads/testing212112323.csv", "disabled": true}, {"key": "languageCode", "value": "hi", "type": "text", "disabled": true}, {"key": "languageCode", "value": "chi", "type": "text"}, {"key": "name", "value": "Mandarin", "type": "text"}, {"key": "enableRtl", "value": "false", "type": "text"}]}, "url": "{{base_url}}/language"}, "response": []}, {"name": "Get sample CSV API", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": "{{base_url}}/language/sample-csv"}, "response": []}, {"name": "Edit Language", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "languageId", "value": "6369e1b60edf4ceb319129ce", "type": "text"}, {"key": "enableRtl", "value": "false", "type": "text"}, {"key": "translationFile", "type": "file", "src": "/home/<USER>/Downloads/sampleFile1667825187716.csv"}, {"key": "languageCode", "value": "chi", "type": "text"}]}, "url": "{{base_url}}/language"}, "response": []}]}, {"name": "Roles", "item": [{"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Add module", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Localization\",\n    \"portalType\": \"SYSTEM_PORTAL\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/role/add-module"}, "response": []}, {"name": "Get portal modules", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/role/modules?portalType=SYSTEM_PORTAL", "host": ["{{base_url}}"], "path": ["role", "modules"], "query": [{"key": "portalType", "value": "SYSTEM_PORTAL"}]}}, "response": []}]}, {"name": "Add role", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"portalType\": \"SYSTEM_PORTAL\",\n    \"name\": \"Operation head1\",\n    \"description\": \"Has access to dashboard and tenants\"\n\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/role"}, "response": []}, {"name": "Edit Role", "request": {"method": "PUT", "header": [{"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Account manager\",\n    \"roleId\": \"62ebb51d95a988f5121cf0d7\",\n    \"description\": \"Testing description\",\n    \"portalType\": \"SYSTEM_PORTAL\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/role"}, "response": []}, {"name": "Edit Role permission", "request": {"method": "PUT", "header": [{"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"roleId\": \"62f33e4f15c14500125ca21b\",\n    \"permission\": {\n        \"Tenants\": {\n            \"view\": true,\n            \"edit\": true,\n            \"delete\": false,\n            \"create\": false\n        },\n        \"Settings\": {\n            \"view\": false,\n            \"edit\": false,\n            \"delete\": false,\n            \"create\": false\n        },\n        \"Localization\": {\n            \"view\": false,\n            \"edit\": false,\n            \"delete\": false,\n            \"create\": false\n        },\n        \"System Users\": {\n            \"create\": false,\n            \"edit\": false,\n            \"view\": false,\n            \"delete\": false\n        },\n        \"Dashboard\" : {\n            \"view\": true,\n            \"edit\": false,\n            \"delete\": false,\n            \"create\": false\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/role/edit-role-permission"}, "response": []}, {"name": "Get roles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/role?portalType=BRANCH_PORTAL", "host": ["{{base_url}}"], "path": ["role"], "query": [{"key": "portalType", "value": "BRANCH_PORTAL", "description": "Filter for portals"}]}}, "response": []}, {"name": "Delete role", "request": {"method": "DELETE", "header": [{"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/role?roleId=62ebb51d95a988f5121cf0d7", "host": ["{{base_url}}"], "path": ["role"], "query": [{"key": "roleId", "value": "62ebb51d95a988f5121cf0d7"}]}}, "response": []}]}, {"name": "System Portal", "item": [{"name": "Tenant branch", "item": [{"name": "Add branch", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1018,\n    \"name\": \"OpenXcell\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/system-portal/tenant-branch"}, "response": []}, {"name": "Update Branch", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"branchId\": \"630dc9f4f111c31fab351e82\",\n    \"name\" :  \"chirag\",\n    \"tenantId\": 1000\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/system-portal/tenant-branch"}, "response": []}, {"name": "Delete branch", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/system-portal/tenant-branch?branchId=630dc9f4f111c31fab351e82&tenantId=1000", "host": ["{{base_url}}"], "path": ["system-portal", "tenant-branch"], "query": [{"key": "branchId", "value": "630dc9f4f111c31fab351e82"}, {"key": "tenantId", "value": "1000"}, {"key": "refreshToken", "value": "{{refreshToken}}", "disabled": true}]}}, "response": []}]}, {"name": "Tenants", "item": [{"name": "Linked tenants", "item": [{"name": "Search tenant", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/system-portal/search-tenant?searchKey=dia", "host": ["{{base_url}}"], "path": ["system-portal", "search-tenant"], "query": [{"key": "search<PERSON>ey", "value": "dia"}]}}, "response": []}, {"name": "Add tenant as linked tenant", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1035,\n    \"LinkingTenantId\": 1001\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/system-portal/linked-tenants"}, "response": []}, {"name": "Remove linked tenant", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/system-portal/linked-tenants?tenantId=1000&LinkingTenantId=1001", "host": ["{{base_url}}"], "path": ["system-portal", "linked-tenants"], "query": [{"key": "tenantId", "value": "1000"}, {"key": "LinkingTenantId", "value": "1001"}]}}, "response": []}]}, {"name": "Add tenant", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"details\": {\n        \"tenantName\": \"eferfe\",\n        \"legalName\": \"ghvg\",\n        \"streetName\": \"jhv\",\n        \"country\": \"62e7d842077fb60012eb35e3\",\n        \"region\": \"631ae5355eece300129f92da\",\n        \"city\": \"64d486272825e600126986a2\",\n        \"mobileNumber\": \"9898209898\",\n        \"countryCode\": \"+91\",\n        \"email\": \"<EMAIL>\",\n        \"firstName\": \"Brijesh\",\n        \"lastName\": \"Darji\"\n    },\n    \"services\": {\n        \"serviceInfo\": [\n            {\n                \"key\": \"collections\",\n                \"permission\": {\n                    \"view\": true,\n                    \"edit\": true,\n                    \"create\": true,\n                    \"delete\": true,\n                    \"key\": \"collections\"\n                }\n            },\n            {\n                \"key\": \"customer_app\",\n                \"permission\": {\n                    \"view\": true,\n                    \"edit\": true,\n                    \"create\": true,\n                    \"delete\": true,\n                    \"key\": \"customer_app\"\n                }\n            },\n            {\n                \"key\": \"tracking\",\n                \"permission\": {\n                    \"view\": true,\n                    \"edit\": true,\n                    \"create\": true,\n                    \"delete\": true,\n                    \"key\": \"tracking\"\n                }\n            },\n            {\n                \"key\": \"deals\",\n                \"permission\": {\n                    \"view\": true,\n                    \"edit\": true,\n                    \"create\": true,\n                    \"delete\": true,\n                    \"key\": \"deals\"\n                }\n            },\n            {\n                \"key\": \"quotations\",\n                \"permission\": {\n                    \"view\": true,\n                    \"edit\": true,\n                    \"create\": true,\n                    \"delete\": true,\n                    \"key\": \"quotations\"\n                }\n            },\n            {\n                \"key\": \"push_notifications\",\n                \"permission\": {\n                    \"view\": true,\n                    \"edit\": true,\n                    \"create\": true,\n                    \"delete\": true,\n                    \"key\": \"push_notifications\"\n                }\n            },\n            {\n                \"key\": \"payments\",\n                \"permission\": {\n                    \"view\": true,\n                    \"edit\": true,\n                    \"create\": true,\n                    \"delete\": true,\n                    \"key\": \"payments\"\n                }\n            }\n        ],\n        \"enableSMSServices\": false,\n        \"hasCustomSenderId\": false,\n        \"smsSenderId\": \"LAWAZIM\"\n    },\n    \"advanced\": {\n        \"limits\": [\n            {\n                \"allowance\": \"56\",\n                \"key\": \"NUMBER_OF_PRODUCTS\"\n            },\n            {\n                \"allowance\": \"12\",\n                \"key\": \"NUMBER_OF_CUSTOMERS\"\n            },\n            {\n                \"allowance\": \"14\",\n                \"key\": \"NUMBER_OF_USERS\"\n            },\n            {\n                \"allowance\": \"25\",\n                \"key\": \"STORAGE_ALLOWANCE\"\n            }\n        ],\n        \"subscription\": {},\n        \"linkedTenants\": []\n    },\n    \"branches\": [\n        {\n            \"branch_id\": \"B01\",\n            \"name\": \"Main\",\n            \"is_default\": true\n        }\n    ],\n    \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/system-portal/tenant"}, "response": []}, {"name": "Get Tenant details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/system-portal/tenant?tenantId={{tenantId}}&tenantQueryType=QUERY_AND_PROJECTION&projection=services", "host": ["{{base_url}}"], "path": ["system-portal", "tenant"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "tenantQueryType", "value": "QUERY_AND_PROJECTION"}, {"key": "projection", "value": "services"}]}}, "response": []}, {"name": "Update tenant", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"details\": {\n        \"tenantName\": \"Kingsman\",\n        \"legalName\": \"Kingsman Pvt Ltd\",\n        \"streetName\": \"Kingsman Street\",\n        \"country\": \"62e7d842077fb60012eb35e3\",\n        \"region\": \"631ae5355eece300129f92da\",\n        \"city\": \"63244af322e3eb003821e8ba\",\n        \"mobileNumber\": **********,\n        \"countryCode\": \"+91\",\n        \"email\": \"<EMAIL>\",\n        \"firstName\": \"Brijesh\",\n        \"lastName\": \"Darji\"\n    },\n    \"services\": {\n        \"serviceInfo\": [\n            {\n                \"key\": \"collections\",\n                \"permission\": {\n                    \"view\": false,\n                    \"create\": false,\n                    \"edit\": false,\n                    \"delete\": false\n                }\n            },\n            {\n                \"key\": \"customer_app\",\n                \"permission\": {\n                    \"view\": true,\n                    \"edit\": true,\n                    \"create\": true,\n                    \"delete\": true\n                }\n            },\n            {\n                \"key\": \"tracking\",\n                \"permission\": {\n                    \"view\": true,\n                    \"edit\": true,\n                    \"create\": true,\n                    \"delete\": true\n                }\n            },\n            {\n                \"key\": \"deals\",\n                \"permission\": {\n                    \"view\": true,\n                    \"edit\": true,\n                    \"create\": true,\n                    \"delete\": true\n                }\n            },\n            {\n                \"key\": \"quotations\",\n                \"permission\": {\n                    \"view\": true,\n                    \"edit\": true,\n                    \"create\": true,\n                    \"delete\": true\n                }\n            },\n            {\n                \"key\": \"push_notifications\",\n                \"permission\": {\n                    \"view\": true,\n                    \"edit\": true,\n                    \"create\": true,\n                    \"delete\": true\n                }\n            },\n            {\n                \"key\": \"payments\",\n                \"permission\": {\n                    \"view\": true,\n                    \"edit\": true,\n                    \"create\": true,\n                    \"delete\": true\n                }\n            }\n        ]\n    },\n    \"advanced\": {\n        \"limits\": [\n            {\n                \"allowance\": 1349,\n                \"key\": \"NUMBER_OF_PRODUCTS\"\n            },\n            {\n                \"allowance\": 200,\n                \"key\": \"NUMBER_OF_CUSTOMERS\"\n            },\n            {\n                \"allowance\": 2000,\n                \"key\": \"NUMBER_OF_USERS\"\n            },\n            {\n                \"allowance\": 2000,\n                \"key\": \"STORAGE_ALLOWANCE\"\n            }\n        ],\n        \"subscription\": {\n            \"from\": \"2022-11-07T12:53:48.331Z\",\n            \"to\": \"2023-02-07T00:00:00.000Z\"\n        },\n        \"linkedTenants\": [\n            1237\n        ]\n    },\n    \"branches\": [\n        {\n            \"branch_id\": \"B01\",\n            \"name\": \"Main\",\n            \"is_default\": true\n        },\n        {\n            \"branch_id\": \"B02\",\n            \"name\": \"Kings Branch 1\",\n            \"is_default\": false\n        }\n    ],\n    \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/system-portal/tenant?tenantId={{tenantId}}", "host": ["{{base_url}}"], "path": ["system-portal", "tenant"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}]}}, "response": []}, {"name": "Tenant list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "deviceToken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/system-portal/get-tenants?page=1&perPage=20&searchKey=", "host": ["{{base_url}}"], "path": ["system-portal", "get-tenants"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "20"}, {"key": "search<PERSON>ey", "value": ""}]}}, "response": []}, {"name": "Chnage tenant owner password", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"type\": \"EMAIL_LINK\",\n    \"tenantId\": 1003\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/system-portal/change-tenant-owner-password"}, "response": []}, {"name": "Delete tenant", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/system-portal/tenant?tenantId=1001", "host": ["{{base_url}}"], "path": ["system-portal", "tenant"], "query": [{"key": "tenantId", "value": "1001"}]}}, "response": []}]}, {"name": "Assigned Tenant", "item": [{"name": "Add assign tenant", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"userRoleId\": \"632458773b186400982d99a6\",\n    \"tenantId\": 1035\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/system-portal/assigned-tenant"}, "response": []}, {"name": "Get assigned tenant list with filter", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/system-portal/assigned-tenant?page=1&perPage=10&userRoleId=632455a36651cd00804becb0", "host": ["{{base_url}}"], "path": ["system-portal", "assigned-tenant"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "userRoleId", "value": "632455a36651cd00804becb0"}]}}, "response": []}, {"name": "Remove assigned tenants", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/system-portal/assigned-tenant?userRoleId=63259330856c01ff3a6c25dc&tenantId=1056", "host": ["{{base_url}}"], "path": ["system-portal", "assigned-tenant"], "query": [{"key": "userRoleId", "value": "63259330856c01ff3a6c25dc"}, {"key": "tenantId", "value": "1056"}]}}, "response": []}]}, {"name": "System Users", "item": [{"name": "List system users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/system-portal/system-users?page=1&perPage=5&searchKey=Sara&roleId=&status=", "host": ["{{base_url}}"], "path": ["system-portal", "system-users"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "5"}, {"key": "search<PERSON>ey", "value": "<PERSON>"}, {"key": "roleId", "value": ""}, {"key": "status", "value": ""}]}}, "response": []}, {"name": "Multiple active/inactive system user", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"users\": [],\n    \"status\": \"ACTIVE\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/system-portal/system-users"}, "response": []}]}, {"name": "Dashboard", "item": [{"name": "Dashboard count", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": "{{base_url}}/system-portal/dashboard"}, "response": []}, {"name": "Dashboard sales state", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/system-portal/dashboardSalesState?tenantId=1119&branchId=634015e7a2a6e700126c563d&type=ALL&durationPeriod=WEEK", "host": ["{{base_url}}"], "path": ["system-portal", "dashboardSalesState"], "query": [{"key": "tenantId", "value": "1119"}, {"key": "branchId", "value": "634015e7a2a6e700126c563d"}, {"key": "type", "value": "ALL", "description": "SALES_PERSON / ALL"}, {"key": "durationPeriod", "value": "WEEK", "description": "DAY / WEEK / MONTH"}]}}, "response": []}]}, {"name": "User", "item": [{"name": "Get system user details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/system-portal/user?userId=6368f0ee3b253000115866a6", "host": ["{{base_url}}"], "path": ["system-portal", "user"], "query": [{"key": "userId", "value": "6368f0ee3b253000115866a6"}]}}, "response": []}, {"name": "Add System portal user", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "firstName", "value": "<PERSON><PERSON><PERSON><PERSON>", "type": "text"}, {"key": "lastName", "value": "Dar<PERSON>", "type": "text"}, {"key": "roleId", "value": "62e8d19e5374737f4c9fa89a", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "mobileNumber", "value": "**********", "type": "text"}, {"key": "countryCode", "value": "+91", "type": "text"}, {"key": "assignedTenants[]", "value": "1035", "type": "text", "disabled": true}, {"key": "assignedTenants[]", "value": "1056", "type": "text", "disabled": true}, {"key": "profilePic", "type": "file", "src": "horse.jpeg", "disabled": true}, {"key": "isActive", "value": "true", "type": "text"}, {"key": "file", "type": "file", "src": []}]}, "url": "{{base_url}}/system-portal/user"}, "response": []}, {"name": "Edit System portal user", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n   \"userRoleId\":\"630ef0c04cb0758d5e45c18f\",\n   \"firstName\":\"<PERSON><PERSON>\",\n   \"lastName\":\"<PERSON><PERSON>\",\n   \"roleId\":\"62e26ce87619e46092f81833\",\n   \"email\":\"<EMAIL>\",\n   \"mobileNumber\":8690935210,\n   \"countryCode\":\"+91\",\n   \"isActive\":true\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/system-portal/user"}, "response": []}, {"name": "delete user", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/system-portal/user?userId=62fdc9f691530c0012328b25", "host": ["{{base_url}}"], "path": ["system-portal", "user"], "query": [{"key": "userId", "value": "62fdc9f691530c0012328b25"}]}}, "response": []}]}, {"name": "Tenant defaults", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": "{{base_url}}/system-portal/get-tenant-defaults"}, "response": []}]}, {"name": "Tenant Portal", "item": [{"name": "Branches", "item": [{"name": "Tenant braches", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/branches?tenantId=1114&type=WAREHOUSE", "host": ["{{base_url}}"], "path": ["tenant-portal", "branches"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "type", "value": "WAREHOUSE"}]}}, "response": []}]}, {"name": "Tenant Portal User", "item": [{"name": "User", "item": [{"name": "Get tenant user details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/user?userRoleId=6369eafc87526f00125f5f04", "host": ["{{base_url}}"], "path": ["tenant-portal", "user"], "query": [{"key": "userRoleId", "value": "6369eafc87526f00125f5f04"}]}}, "response": []}, {"name": "Add user", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "tenantId", "value": "1018", "type": "text"}, {"key": "portalType", "value": "TENANT_PORTAL", "description": "from which portal this api has been called", "type": "text"}, {"key": "countryCode", "value": "+91", "type": "text"}, {"key": "mobileNumber", "value": "9999999999", "type": "text"}, {"key": "firstName", "value": "Nooh", "type": "text"}, {"key": "lastName", "value": "<PERSON><PERSON>", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "createNewUserRoleId", "value": "65a764de1088ab00121ee92a", "type": "text"}, {"key": "profilePic", "type": "file", "src": [], "disabled": true}, {"key": "roleId", "value": "633c324008805fde7360f461", "type": "text"}, {"key": "branchId", "value": "631600e0b57fa7001218a4f2", "type": "text"}, {"key": "isActive", "value": "true", "type": "text"}, {"key": "allowPriceChange", "value": "true", "type": "text"}, {"key": "allowPostingVoucherReceipts", "value": "true", "type": "text", "disabled": true}, {"key": "notifications[]", "value": "{\"notification_type\":\"NEW_ORDER\",\"allow_push_notification\":true,\"allow_sms_notification\":true,\"allow_email_notification\":true}", "type": "text"}, {"key": "notifications[]", "value": "{\"notification_type\":\"ORDER_SHIPPED\",\"allow_push_notification\":true,\"allow_sms_notification\":true,\"allow_email_notification\":true}", "type": "text"}, {"key": "notifications[]", "value": "{\"notification_type\":\"ORDER_DELIVERED\",\"allow_push_notification\":true,\"allow_sms_notification\":true,\"allow_email_notification\":true}", "type": "text"}, {"key": "notifications[]", "value": "{\"notification_type\":\"ORDER_PREPARING\",\"allow_push_notification\":true,\"allow_sms_notification\":true,\"allow_email_notification\":true}", "type": "text"}, {"key": "supervisorId", "value": "62e275ac0b77a6bbdf16fac8", "type": "text", "disabled": true}]}, "url": "{{base_url}}/tenant-portal/user"}, "response": []}, {"name": "Update tenant/branch user", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"userRoleId\": \"63d7c3ec099872001245c559\",\n    \"firstName\": \"<PERSON><PERSON><PERSON>\",\n    \"lastName\": \"<PERSON><PERSON><PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"mobileNumber\": 6352968563,\n    \"countryCode\": \"+91\",\n    \"isActive\": true,\n    \"portalType\": \"TENANT_PORTAL\",\n    \"allowPriceChange\": true,\n    \"roleId\": \"650137d9206885ba461dd2ab\",\n    \"notifications\": [\n        {\n            \"notification_type\": \"NEW_ORDER\",\n            \"allow_push_notification\": true,\n            \"allow_sms_notification\": true,\n            \"allow_email_notification\": true\n        },\n        {\n            \"notification_type\": \"ORDER_SHIPPED\",\n            \"allow_push_notification\": true,\n            \"allow_sms_notification\": true,\n            \"allow_email_notification\": true\n        },\n        {\n            \"notification_type\": \"ORDER_DELIVERED\",\n            \"allow_push_notification\": true,\n            \"allow_sms_notification\": true,\n            \"allow_email_notification\": true\n        },\n        {\n            \"notification_type\": \"ORDER_PREPARING\",\n            \"allow_push_notification\": true,\n            \"allow_sms_notification\": true,\n            \"allow_email_notification\": true\n        }\n    ],\n    \"tenantId\": 1131,\n    \"newSalesPersonId\": \"63d7c52d099872001245c695\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/user"}, "response": []}, {"name": "Delete user", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/user?userRoleId=63d7c3ec099872001245c559&newSalesPersonId=64c892d6d2e6f000120b590d", "host": ["{{base_url}}"], "path": ["tenant-portal", "user"], "query": [{"key": "userRoleId", "value": "63d7c3ec099872001245c559"}, {"key": "newSalesPersonId", "value": "64c892d6d2e6f000120b590d"}]}}, "response": []}]}, {"name": "Users", "item": [{"name": "Get tenant user list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/users?tenantId={{tenantId}}&page=1&perPage=10&type=PAGINATION&searchKey=", "host": ["{{base_url}}"], "path": ["tenant-portal", "users"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "type", "value": "PAGINATION"}, {"key": "search<PERSON>ey", "value": ""}, {"key": "branchId", "value": "6333f1fb67e853cb742142a2", "disabled": true}]}}, "response": []}, {"name": "Update user status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}`", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"userRoleIds\": [\n        \"636a160a87526f00125faf42\",\n        \"63d7c52d099872001245c695\",\n        \"65a7e5055d04c3001235bba9\"\n    ],\n    \"status\": \"ACTIVE\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/users", "description": "Api for set user status to active or in active"}, "response": []}]}, {"name": "User settings", "item": [{"name": "Get setting configurations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/userSetting?tenantId=1119", "host": ["{{base_url}}"], "path": ["tenant-portal", "userSetting"], "query": [{"key": "tenantId", "value": "1119"}]}}, "response": []}, {"name": "Update user settings", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"masterPriceId\": \"63906a580eb78c00125ddba8\",\n    \"out_of_stock\": {\n        \"visible\": true,\n        \"searchable\": true\n    },\n    \"priceChange\": false,\n    \"preferredLanguage\": \"en\",\n    \n    \"tenantId\": 1119\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/updateUserSetting"}, "response": []}]}, {"name": "Get User Details by Ids", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"ids\": [\n        \"636906513b25300011587a90\",\n        \"64c892d6d2e6f000120b590d\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/usersDetails"}, "response": []}]}, {"name": "Tenant customer", "item": [{"name": "Customer devices", "item": [{"name": "Add customer device", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\"userRoleId\" : \"635f7a64374f70490cf10af2\",\n\"deviceId\" : \"13254897NIAGDFnAASFDWA2ASSN\",\n\"deviceType\" : \"MOBILE\",\n\"deviceOs\" : \"ANDROID\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/tenant-portal/customer-device-access?", "host": ["{{base_url}}"], "path": ["tenant-portal", "customer-device-access"], "query": [{"key": "", "value": null}]}}, "response": []}, {"name": "Delete Customer device", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\"userRoleId\" : \"635f7a64374f70490cf10af2\",\n\"deviceId\" : \"13254897NIAGDFnAASFDWA2ASSN\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/customer-device-access"}, "response": []}]}, {"name": "Customer", "item": [{"name": "Get customer details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "deviceaccesstype", "value": "{{deviceaccesstype}}", "type": "text"}, {"key": "devicetype", "value": "{{devicetype}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/customer?tenantId=1131&customerUserRoleIds=65263d91e018db00122863c8&customerUserRoleIds=641d40d25fa74e001285ff85", "host": ["{{base_url}}"], "path": ["tenant-portal", "customer"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "userRoleId", "value": "65263d91e018db00122863c8", "disabled": true}, {"key": "customerUserRoleIds", "value": "65263d91e018db00122863c8"}, {"key": "customerUserRoleIds", "value": "641d40d25fa74e001285ff85"}, {"key": "customerUserRoleIds[]", "value": "641b093ef4fac2a95c0e77d3", "disabled": true}, {"key": "projections[]", "value": "customer_name", "disabled": true}, {"key": "projections[]", "value": "customer_legal_name", "disabled": true}, {"key": "projections[]", "value": "customer_first_name", "disabled": true}, {"key": "projections[]", "value": "customer_last_name", "disabled": true}]}}, "response": []}, {"name": "Delete customer", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/customer?userRoleId=642bec1a283085ba09fd953c", "host": ["{{base_url}}"], "path": ["tenant-portal", "customer"], "query": [{"key": "userRoleId", "value": "642bec1a283085ba09fd953c"}]}}, "response": []}, {"name": "Edit customer", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refeshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"accountNumber\": \"dg123\",\n    \"customerName\": \"Deep\",\n    \"legalName\": \"Deep Gajjar\",\n    \"salesPersonId\": \"652616abe018db0012283545\",\n    \"priceListId\": \"65263d3ed5e2ed3184c7b894\",\n    \"shippingAddress\": \"Openxcell\",\n    \"shippingCountryId\": \"62e7d842077fb60012eb35e3\",\n    \"shippingCityId\": \"63244af322e3eb003821e8ba\",\n    \"shippingRegionId\": \"631ae5355eece300129f92da\",\n    \"shippingCountryCode\": \"+91\",\n    \"shippingMobileNumber\": **********,\n    \"latitude\": 0,\n    \"longitude\": 0,\n    \"firstName\": \"Deep\",\n    \"lastName\": \"Customer\",\n    \"email\": \"<EMAIL>\",\n    \"preferredLanguage\": \"en\",\n    \"customerAppAccess\": true,\n    \"customerCatalogMode\": true,\n    \"mobileNumber\": **********,\n    \"countryCode\": \"+91\",\n    \"notifications\": [\n        {\n            \"notification_type\": \"NEW_ORDER\",\n            \"allow_push_notification\": true,\n            \"allow_sms_notification\": true,\n            \"allow_email_notification\": true\n        },\n        {\n            \"notification_type\": \"ORDER_DELIVERED\",\n            \"allow_push_notification\": true,\n            \"allow_sms_notification\": true,\n            \"allow_email_notification\": true\n        },\n        {\n            \"notification_type\": \"ORDER_SHIPPED\",\n            \"allow_push_notification\": true,\n            \"allow_sms_notification\": true,\n            \"allow_email_notification\": true\n        },\n        {\n            \"notification_type\": \"ORDER_PREPARING\",\n            \"allow_push_notification\": true,\n            \"allow_sms_notification\": true,\n            \"allow_email_notification\": true\n        }\n    ],\n    \"deviceAccess\": [\n        {\n            \"device_id\": \"ab314825-80a8-474f-8547-557c82b2f433\",\n            \"type\": \"MOBILE\",\n            \"os\": \"IOS\"\n        }\n    ],\n    \"isActive\": true,\n    \"tenantId\": 1241,\n    \"userRoleId\": \"65263d91e018db00122863c8\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/customer"}, "response": []}, {"name": "Add Customer", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"accountNumber\": \"WTYU5112\",\n    \"customerName\": \"<PERSON><PERSON><PERSON>sh Customer\",\n    \"legalName\": \"Brijesh Customer Pvt Ltd\",\n    \"salesPersonId\": \"6369eafc87526f00125f5f04\",\n    \"priceListId\": \"63771ca7ba5634438e6c7c46\",\n    \"shippingAddress\": \"Mayur flats, 2, Motinagar Society, Paldi, Ahmedabad, Gujarat 380007, India\",\n    \"shippingCountryId\": \"62e7d842077fb60012eb35e3\",\n    \"shippingCityId\": \"63244af322e3eb003821e8ba\",\n    \"shippingRegionId\": \"631ae5355eece300129f92da\",\n    \"shippingCountryCode\": \"+91\",\n    \"shippingMobileNumber\": **********,\n    \"latitude\": 23.**************,\n    \"longitude\": 72.**************,\n    \"firstName\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"lastName\": \"<PERSON><PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"preferredLanguage\": \"en\",\n    \"customerAppAccess\": true,\n    \"customerCatalogMode\": false,\n    \"mobileNumber\": **********,\n    \"countryCode\": \"+91\",\n    \"notifications\": [\n        {\n            \"notification_type\": \"NEW_ORDER\",\n            \"allow_push_notification\": true,\n            \"allow_sms_notification\": true,\n            \"allow_email_notification\": true\n        },\n        {\n            \"notification_type\": \"ORDER_PREPARING\",\n            \"allow_push_notification\": true,\n            \"allow_sms_notification\": true,\n            \"allow_email_notification\": true\n        },\n        {\n            \"notification_type\": \"ORDER_SHIPPED\",\n            \"allow_push_notification\": true,\n            \"allow_sms_notification\": true,\n            \"allow_email_notification\": true\n        },\n        {\n            \"notification_type\": \"ORDER_DELIVERED\",\n            \"allow_push_notification\": true,\n            \"allow_sms_notification\": true,\n            \"allow_email_notification\": true\n        }\n    ],\n    \"deviceAccess\": [\n        {\n            \"device_id\": \"c1e37b93-a42e-4bef-9084-7f99dcbd5265\",\n            \"type\": \"TABLET\",\n            \"os\": \"IOS\"\n        },\n        {\n            \"device_id\": \"c1e37b93-a42e-4bef-9084-7f99dcbd5265\",\n            \"type\": \"TABLET\",\n            \"os\": \"IOS\"\n        },\n        {\n            \"device_id\": \"c1e37b93-a42e-4bef-9084-7f99dcbd5265\",\n            \"type\": \"TABLET\",\n            \"os\": \"IOS\"\n        },\n        {\n            \"device_id\": \"c1e37b93-a42e-4bef-9084-7f99dcbd5265\",\n            \"type\": \"TABLET\",\n            \"os\": \"IOS\"\n        },\n        {\n            \"device_id\": \"c1e37b93-a42e-4bef-9084-7f99dcbd5265\",\n            \"type\": \"TABLET\",\n            \"os\": \"IOS\"\n        },\n        {\n            \"device_id\": \"c1e37b93-a42e-4bef-9084-7f99dcbd5265\",\n            \"type\": \"TABLET\",\n            \"os\": \"IOS\"\n        },\n        {\n            \"device_id\": \"c1e37b93-a42e-4bef-9084-7f99dcbd5265\",\n            \"type\": \"TABLET\",\n            \"os\": \"IOS\"\n        },\n        {\n            \"device_id\": \"c1e37b93-a42e-4bef-9084-7f99dcbd5265\",\n            \"type\": \"TABLET\",\n            \"os\": \"IOS\"\n        },\n        {\n            \"device_id\": \"c1e37b93-a42e-4bef-9084-7f99dcbd5265\",\n            \"type\": \"TABLET\",\n            \"os\": \"IOS\"\n        },\n        {\n            \"device_id\": \"c1e37b93-a42e-4bef-9084-7f99dcbd5265\",\n            \"type\": \"TABLET\",\n            \"os\": \"IOS\"\n        },\n        {\n            \"device_id\": \"c1e37b93-a42e-4bef-9084-7f99dcbd5265\",\n            \"type\": \"TABLET\",\n            \"os\": \"IOS\"\n        },\n        {\n            \"device_id\": \"Testing from Postman\",\n            \"type\": \"MOBILE\",\n            \"os\": \"IOS\"\n        }\n    ],\n    \"isActive\": true,\n    \"tenantId\": 1131,\n    \"userRoleId\": \"639ae010c871790012410b65\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/customer"}, "response": []}]}, {"name": "Customers", "item": [{"name": "Update Customers", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"ids\": [\n        \"65263d91e018db00122863c8\"\n    ],\n    \"updateFields\": {\n        \"external_id\": \"C20311\"\n    },\n    \"tenantId\": {{tenantId}}\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/customers"}, "response": []}, {"name": "Update customers ( app access & status )", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"customers\": [\"63ae7e9338d5b8aeb560c6c3\"],\n    \"statusType\": \"APP_ACCESS\",\n    \"status\": \"ACTIVE\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/customers"}, "response": []}, {"name": "Tenant Customer listing", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/customers?tenantId=1131&customerType=ALL", "host": ["{{base_url}}"], "path": ["tenant-portal", "customers"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "customerType", "value": "ALL", "description": "ALL / NEW / UNASSIGNED / INACTIVE / APP_ACCESS / ACTIVE / APP_REQUEST"}, {"key": "sortType", "value": "DESC", "disabled": true}, {"key": "sortByName", "value": "true", "disabled": true}, {"key": "supervisorId", "value": "64d21e600e65e5001215120d", "disabled": true}, {"key": "type", "value": "SEARCH", "description": "ALL / SEARCH / PAGINATION", "disabled": true}, {"key": "onlyCustomerCount", "value": "true", "disabled": true}, {"key": "salesPersonId", "value": "63d7c3ec099872001245c559", "disabled": true}, {"key": "search<PERSON>ey", "value": "f65", "disabled": true}]}}, "response": []}]}, {"name": "Verify Customer", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"action\": \"SEND_OTP\",\n    \"countryCode\": \"+91\",\n    \"mobileNumber\": \"9898989898\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/verifyCustomer"}, "response": []}, {"name": "List Customers for payment", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/tenant-portal/customerPayment?tenantId={{tenantId}}&page=1&perPage=10&type=WITHOUT_PAGINATION", "host": ["{{base_url}}"], "path": ["tenant-portal", "customerPayment"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "search<PERSON>ey", "value": "rel", "disabled": true}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "salesPersonId", "value": "652616abe018db0012283545", "disabled": true}, {"key": "type", "value": "WITHOUT_PAGINATION"}, {"key": "filters", "value": "dg127", "disabled": true}, {"key": "filters", "value": "dg124", "disabled": true}]}}, "response": []}, {"name": "List Customers for Reward Program", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/tenant-portal/customerRewardProgram?searchKey=as1&page=1&perPage=10&type=PENDING&tenantId={{tenantId}}", "host": ["{{base_url}}"], "path": ["tenant-portal", "customerRewardProgram"], "query": [{"key": "search<PERSON>ey", "value": "as1"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "salesPersonId", "value": "652616abe018db0012283545", "disabled": true}, {"key": "type", "value": "PENDING"}, {"key": "filters", "value": "", "disabled": true}, {"key": "filters", "value": "dg124", "disabled": true}, {"key": "tenantId", "value": "{{tenantId}}"}]}}, "response": []}, {"name": "Update customer common address", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"shipping_address\": \"testing address\",\n    \"shipping_country_id\": \"62e7d842077fb60012eb35e3\",\n    \"shipping_city_id\": \"63244af322e3eb003821e8ba\",\n    \"shipping_region_id\": \"631ae5355eece300129f92da\",\n    \"gps_coordinates\": { \n        \"longitude\": 12.1,\n        \"latitude\": 22.9\n    },\n    \"shipping_country_code\": \"+91\",\n    \"shipping_mobile_number\": \"8460353555\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/customerCommonAddress"}, "response": []}, {"name": "Search customer with mobile number", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/searchCustomerByMobileNumber?countryCode=%2B91&tenantId=1004&mobileNumber=**********", "host": ["{{base_url}}"], "path": ["tenant-portal", "searchCustomerByMobileNumber"], "query": [{"key": "countryCode", "value": "%2B91"}, {"key": "tenantId", "value": "1004"}, {"key": "mobileNumber", "value": "**********"}]}}, "response": []}, {"name": "Check Existing External Id", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/customer/checkExternalId?tenantId=1131&accountNumber=WTYU5112", "host": ["{{base_url}}"], "path": ["tenant-portal", "customer", "checkExternalId"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "accountNumber", "value": "WTYU5112"}]}}, "response": []}, {"name": "App access request update", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"customerUserRoleIds\": [\n        \"64105b17df65f90012cb0c1d\",\n        \"6399c66ac871790012407edd\"\n    ],\n    \"accessInfo\": {\n        \"appAccess\": true,\n        \"catalogMode\": false\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/customerAppAccess"}, "response": []}]}, {"name": "Supervisors", "item": [{"name": "Get tenant supervisors", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/supervisors?tenantId=1114", "host": ["{{base_url}}"], "path": ["tenant-portal", "supervisors"], "query": [{"key": "tenantId", "value": "1114"}]}}, "response": []}]}, {"name": "Sales person", "item": [{"name": "Get sales person listing", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/salesperson?tenantId={{tenantId}}&isActive=true", "host": ["{{base_url}}"], "path": ["tenant-portal", "salesperson"], "query": [{"key": "branchId", "value": "6315a0c6dd989f00115c92a6", "disabled": true}, {"key": "tenantId", "value": "{{tenantId}}"}, {"key": "isActive", "value": "true"}]}}, "response": []}]}, {"name": "Tenant price list", "item": [{"name": "price list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/price-list?tenantId=1035", "host": ["{{base_url}}"], "path": ["tenant-portal", "price-list"], "query": [{"key": "tenantId", "value": "1035"}]}}, "response": []}]}, {"name": "Payment", "item": [{"name": "Update Payment Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"status\": true,\n    \"userRoleIds\": [\n        \"65263d91e018db00122863c8\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/allowPayment"}, "response": []}]}, {"name": "Payment Terms", "item": [{"name": "List Payment Terms", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/tenant-portal/paymentTerms?tenantId={{tenantId}}", "host": ["{{base_url}}"], "path": ["tenant-portal", "paymentTerms"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}]}}, "response": []}]}, {"name": "Tenant Configurations", "item": [{"name": "Get Configuration", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/configurations?tenantId=1114&type=APP_SETTING", "host": ["{{base_url}}"], "path": ["tenant-portal", "configurations"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "type", "value": "APP_SETTING", "description": "SHIPPING_LABEL / APP_SETTING "}]}}, "response": []}, {"name": "Update Shipping Label", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1131,\n    \"tenantLegalName\": \"Kingsman1 Pvt Ltd\",\n    \"branches\": [\n        {\n            \"branchId\": \"636906513b25300011587a95\",\n            \"name\": \"Main\",\n            \"streetAddress\": \"Kingsman Street\",\n            \"region\": \"631ae5355eece300129f92da\",\n            \"city\": \"63244af322e3eb003821e8ba\",\n            \"mobileNumber\": 9080706050,\n            \"mobileNumberCountryCode\": \"+91\",\n            \"phoneNumber\": 9080706050,\n            \"phoneNumberCountryCode\": \"+91\"\n        }\n    ],\n    \"shippingLabelLogo\": \"hawak-logo-black.svg\",\n    \"isActive\": false\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/configurations"}, "response": []}, {"name": "Update App Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"tenantId\": 1241,\r\n    \"quantityLabel\": 10,\r\n    \"considerNewItem\": 30,\r\n    \"priceChange\": true,\r\n    \"hideOutOfStockProduct\": false,\r\n    \"reduceInventory\": false,\r\n    \"customerAppAccess\": true,\r\n    \"catalogMode\": true,\r\n    \"customerAutoCatalogMode\": {\r\n        \"enabled\": \"true\",\r\n        \"duration\": \"20\"\r\n    },\r\n    \"paymentVoucherWhatsappNotification\": false,\r\n    \"preferredLanguageId\": \"en\",\r\n    \"counter\": 100000,\r\n    \"prefix\": \"\",\r\n    \"decimalPoint\": 2,\r\n    \"preferredLanguage\": \"en\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/configurations"}, "response": []}]}, {"name": "Tenant Allow Validation", "item": [{"name": "Tenant active customers and users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/allowNewValidation?tenantId=1114&type=USERS", "host": ["{{base_url}}"], "path": ["tenant-portal", "allowNewValidation"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "type", "value": "USERS", "description": "CUSTOMERS / USERS"}]}}, "response": []}]}, {"name": "Datasheet", "item": [{"name": "Export datasheet", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": {{tenantId}},\n    \"dataType\": \"PRODUCT\",\n    \"operationType\": \"UPDATE\",\n    \"apiVersion\": 2\n}\n", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/dataSheet"}, "response": []}, {"name": "Get column list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/columnField?dataType=CUSTOMER", "host": ["{{base_url}}"], "path": ["tenant-portal", "columnField"], "query": [{"key": "dataType", "value": "CUSTOMER", "description": "CUSTOMER / PRODUCT"}]}}, "response": []}, {"name": "Get upload file signature", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": {{tenantId}},\n    \"dataType\": \"PRICE\",\n    \"operationType\": \"UPDATE\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/dataSheet/import/getUploadSignature"}, "response": []}, {"name": "Update upload excel file", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1241,\n    \"dataType\": \"PRODUCT\",\n    \"operationType\": \"UPDATE\",\n    \"fileName\": \"product_2025-03-10_1741583821037.xlsx\",\n    \"updateType\": \"DETAILS\",\n    \"isReUpload\": true,\n    \"originalFileName\": \"product.xlsx\",\n    \"apiVersion\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/dataSheet/import/updateUploadFile"}, "response": []}, {"name": "Tenant datasheet listing", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/dataSheet?tenantId={{tenantId}}&page=1&perPage=10&searchKey=price", "host": ["{{base_url}}"], "path": ["tenant-portal", "dataSheet"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "search<PERSON>ey", "value": "price"}]}}, "response": []}, {"name": "Import validation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"fileId\": \"66bc480e12ad20001158f918\",\n    \"tenantId\": 1241,\n    \"approveType\": \"ALL\",\n    \"apiVersion\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/dataSheet/import/validation"}, "response": []}, {"name": "Delete datasheets", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1130,\n    \"fileIds\" : [\"63e500ddf0a247001251f572\"]\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/dataSheet"}, "response": []}]}, {"name": "Hold Reason", "item": [{"name": "Hold Reason Template Options", "item": [{"name": "Get Hold Reason Template Options List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "text"}, {"key": "devicetoken", "value": "3a800c12-1d93-4f21-aa15-e1662ebb1188", "type": "text"}, {"key": "refreshtoken", "value": "eyJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiUlNBLU9BRVAifQ.T2w7xXq87fD2jhValNG1c27ShbcZqh-p-QduITYskycN6KuLO-mKBLIyZYhwGczRgiZ3ZcPG1v8TjhysxL8xNuBQ4oHtYxeW1nhsnuPc288u_VstmG6z0XOHezaIJJ1jcWBgSE0x2ZMUvnESfxJpY-Nme3p0V3ZD832KoLAKpQa2kAxiATKRP4TCqXA-Te6XJyE1nnK_82Ff-Dfukpm2hy6E5k4HgF3K8vqN56QSAszeUZMdkhXe6Rt5ISsg5qPBgPYe8Wx04iQtKsYuGhu57Pun5Yx6p6tyyxKIe48WO0bAVd1mIwqSwOfWKP_ipZDab010T6f4CWXllbGDiruRXQ.gUnSnya9E_C1MimS.A80k9pASM4P5wMStxCO0r_Mbv1eoEWJBMXk37edZKpKlNWPRzHjBVCtMfCnmzwFqNREQIW7fu7QLpjajU5NHPnpJ71nAftGc6n7yQS2UW3w3VbuvjF21EZhJj1ZqlDIpSlqHCXsX7O8utnwJSV7DEm4gy0034Kp23R7BEgMGO9uLR-BIeAgRi0wjTpQsKMdcNJpBOcyYeWMQY2JUjDyf1kTvefVfqWkfO_0gyNf8xUgmshrf7H3DCY6vMcAGvag21b2NzrQ35Qrj4kDraGjQjsWD-ieyvOSHH3s7LEusTaRu8lx-q1IN402BoS_GtywSP046m3fTIOTwg8VDNLkGjPZh8dBB6Aqnsz9lDZ-g70V5YwUCDJzyd-KufLE9Rn7ffZITt8lmALe031dw0E1cR23QX4tddH6VaelolrRFsc2vPkrRQ8ljUM3pgslW2jz1IDVpv4Cs1qpwm-hoComFb51mb9XDQ1Z7kMlmRB3mIfuIENQnbcFIfSPQgSBG8x-wBDzyP_chAFLKYkSMl6sY7D_Sgw3_DLtzGoJD6XvjLy0A0gvQFy9ZVlnL4F4iP3PQqp1jXk4qOUjRnpd2V39Hv4otJFLrkvIpV9zP0Rji-FZrocmY3Y9qP0Fspb3Amx6f2G3wgmNCcqwYulb61-2lwYY5qyOhRgDpuXn0YKX0NvwL0U2L3pF-WiNceT4oK5_zNyb89FKakkG_mIA1jvjKp6d-RQAm-4CsKQ9hqRSGW8YHqZYYjrEL5AexF7ZnhCtS8_coWhZ-rPU8UAf_ytbyBZAXAdFuAOuxkDPJhyoeFTZ8eiGR-7KAnsc2DGWT6LlBI_506g9RvYuF4utEWGU-KB6HKD8XA8hM1Ef9vVKGY-k5DVrm67NbnJ_iFAjXdSDSTAp1DNtt5qwmO1xqGUuE-ZFQ1d2tbDexViwQaKgIriulLG7GgQ-TI9pEWaAw6bITs8sZeex96YMm7zgftALGznyJkvWJ60SIkfibvn8P2kOgOhzubRKfvyDm9okSOUrMBRo7MSk5Y0XCJhKzh3gc6VRmtkYhtPjlXhBOV37uLkxB2PaBbZImwxT-kXKJ2vxbZCZivmSqBeD5aCTrofAAS0yOFqA7gF6Svn9QzrXzidJW8r3XNUAOaDVS0_kq2aPCNuVoJMUX5W2Qf0EqZXAyOi72QHiA2OmOnL1ZCBoiuSmLbUEj2a31W6KDsXGgHdXXOY7JqAuNbPPk88r1fxtjHihJo8YVfL-z58PweJUpwl3w5_Vw7EYItMtoDv8s5Zq1t9GmEqGFOidSXGPkFqE49yWhxnqdh1zAXrlnFMlH56P2YETcZYorL6D1Rh_xuA.695aWnB_bHq5zvbgFDG-kA", "type": "text"}, {"key": "userroleid", "value": "651abbcf1b4b3b0012c6bacf", "type": "text"}], "url": "{{base_url}}/holdReasonTemplateOptions"}, "response": []}]}, {"name": "Create Hold Reason", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1114,\n    \"title\": \"Hold Reason Template\",\n    \"secondaryLanguageTitle\": \"Hold Reason Template\",\n    \"holdTemplates\": [\n        {\n            \"templateId\": \"26905281-c98e-4741-8976-35afef5baee4\",\n            \"title\": \"Order Release - By Customer - English\",\n            \"parameters\": [\n                {\n                    \"system_key\": \"legal_name\",\n                    \"integration_key\": \"legal_name\"\n                }\n            ],\n            \"language\": \"en\",\n            \"role\": \"Sales Person\"\n        },\n        {\n            \"templateId\": \"26905281-c98e-4741-8976-35afef5baee4\",\n            \"title\": \"Order Release - By Customer - English\",\n            \"parameters\": [\n                {\n                    \"system_key\": \"legal_name\",\n                    \"integration_key\": \"legal_name\"\n                }\n            ],\n            \"language\": \"ar\",\n            \"role\": \"Sales Person\"\n        },\n        {\n            \"templateId\": \"26905281-c98e-4741-8976-35afef5baee4\",\n            \"title\": \"Order Release - By Customer - English\",\n            \"parameters\": [\n                {\n                    \"system_key\": \"legal_name\",\n                    \"integration_key\": \"legal_name\"\n                }\n            ],\n            \"language\": \"en\",\n            \"role\": \"Customer\"\n        },\n        {\n            \"templateId\": \"26905281-c98e-4741-8976-35afef5baee4\",\n            \"title\": \"Order Release - By Customer - English\",\n            \"parameters\": [\n                {\n                    \"system_key\": \"legal_name\",\n                    \"integration_key\": \"legal_name\"\n                }\n            ],\n            \"language\": \"ar\",\n            \"role\": \"Customer\"\n        }\n    ],\n    \"releaseTemplates\": [\n        {\n            \"templateId\": \"26905281-c98e-4741-8976-35afef5baee4\",\n            \"title\": \"Order Release - By Customer - English\",\n            \"parameters\": [\n                {\n                    \"system_key\": \"legal_name\",\n                    \"integration_key\": \"legal_name\"\n                }\n            ],\n            \"language\": \"en\",\n            \"role\": \"Sales Person\"\n        }\n    ],\n    \"isWhatsappMessageEnabled\": true,\n    \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/holdReason"}, "response": []}, {"name": "Get Template Details From The Integration", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/holdReason/template?tenantId=1131&templateId=26905281-c98e-4741-8976-35afef5baee4", "host": ["{{base_url}}"], "path": ["holdReason", "template"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "templateId", "value": "26905281-c98e-4741-8976-35afef5baee4"}]}}, "response": []}, {"name": "Get Template List By Package", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/holdReason/templates?tenantId=1131", "host": ["{{base_url}}"], "path": ["holdReason", "templates"], "query": [{"key": "tenantId", "value": "1131"}]}}, "response": []}, {"name": "Create Hold Reason To All Tenant", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": "{{base_url}}/holdReason/addHoldReasonToAllTenant"}, "response": []}, {"name": "Get Hold Reason List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/holdReason?tenantId=1114&status=ACTIVE", "host": ["{{base_url}}"], "path": ["holdReason"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "status", "value": "ACTIVE"}]}}, "response": []}, {"name": "Update Hold Reason", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"id\": \"656dfe89738fecf419dff900\",\n    \"tenantId\": 1114,\n    \"title\": \"Hold Reason Template\",\n    \"secondaryLanguageTitle\": \"Hold Reason Template\",\n    \"holdTemplates\": [\n        {\n            \"templateId\": \"26905281-c98e-4741-8976-35afef5baee4\",\n            \"title\": \"Order Release - By Customer - English\",\n            \"parameters\": [\n                {\n                    \"system_key\": \"legal_name\",\n                    \"integration_key\": \"legal_name\"\n                }\n            ],\n            \"language\": \"en\",\n            \"role\": \"Sales Person\"\n        },\n        {\n            \"templateId\": \"26905281-c98e-4741-8976-35afef5baee4\",\n            \"title\": \"Order Release - By Customer - English\",\n            \"parameters\": [\n                {\n                    \"system_key\": \"legal_name\",\n                    \"integration_key\": \"legal_name\"\n                }\n            ],\n            \"language\": \"ar\",\n            \"role\": \"Sales Person\"\n        },\n        {\n            \"templateId\": \"26905281-c98e-4741-8976-35afef5baee4\",\n            \"title\": \"Order Release - By Customer - English\",\n            \"parameters\": [\n                {\n                    \"system_key\": \"legal_name\",\n                    \"integration_key\": \"legal_name\"\n                }\n            ],\n            \"language\": \"en\",\n            \"role\": \"Customer\"\n        },\n        {\n            \"templateId\": \"26905281-c98e-4741-8976-35afef5baee4\",\n            \"title\": \"Order Release - By Customer - English\",\n            \"parameters\": [\n                {\n                    \"system_key\": \"legal_name\",\n                    \"integration_key\": \"legal_name\"\n                }\n            ],\n            \"language\": \"ar\",\n            \"role\": \"Customer\"\n        }\n    ],\n    \"releaseTemplates\": [\n        {\n            \"templateId\": \"26905281-c98e-4741-8976-35afef5baee4\",\n            \"title\": \"Order Release - By Customer - English\",\n            \"parameters\": [\n                {\n                    \"system_key\": \"legal_name\",\n                    \"integration_key\": \"legal_name\"\n                }\n            ],\n            \"language\": \"en\",\n            \"role\": \"Sales Person\"\n        }\n    ],\n    \"isWhatsappMessageEnabled\": true,\n    \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/holdReason"}, "response": []}, {"name": "Get Template list with details to hold reason and release reason", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/holdReason/reasonTemplateWithDetails?tenantId=1131&holdReasonId=658bf22c096cd031d1124abe&type=hold_templates&orderId=667c244c6be9309e27742304", "host": ["{{base_url}}"], "path": ["holdReason", "reasonTemplateWithDetails"], "query": [{"key": "tenantId", "value": "1131"}, {"key": "holdReasonId", "value": "658bf22c096cd031d1124abe"}, {"key": "type", "value": "hold_templates"}, {"key": "orderId", "value": "667c244c6be9309e27742304"}]}}, "response": []}]}, {"name": "Statement", "item": [{"name": "Account <PERSON><PERSON>", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}, {"key": "devicetype", "value": "{{devicetype}}", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/tenant-portal/accountBalance?customerExternalId=C20009&agingDate=********", "host": ["{{base_url}}"], "path": ["tenant-portal", "accountBalance"], "query": [{"key": "customerExternalId", "value": "C20009"}, {"key": "agingDate", "value": "********", "description": "yyyyMMdd"}]}}, "response": []}, {"name": "Account Statements", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/accountStatements?customerExternalId=C20311&fromDate=********&toDate=********&page=1&perPage=10", "host": ["{{base_url}}"], "path": ["tenant-portal", "accountStatements"], "query": [{"key": "customerExternalId", "value": "C20311"}, {"key": "fromDate", "value": "********"}, {"key": "toDate", "value": "********"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "Generate Account Statements", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"customerExternalId\": \"C20311\",\n    \"agingDate\": \"********\",\n    \"fromDate\": \"********\",\n    \"toDate\": \"********\",\n    \"tenantId\": 1241,\n    \"timezone\": \"Asia/Mumbai\",\n    \"agingSecondaryTitle\": \"Aging Report\",\n    \"statementSecondaryTitle\": \"Statement of Account\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/accountStatements"}, "response": []}]}, {"name": "Reward Program", "item": [{"name": "Calculator", "item": [{"name": "SAP Points", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"paymentAmount\": 3000,\n    \"aging\": {\n        \"balance_0_30_days\": 0,\n        \"balance_31_60_days\": 0,\n        \"balance_61_90_days\": 0,\n        \"balance_91_120_days\": 2000,\n        \"balance_120_more_days\": 1000\n    },\n    \"reward\": {\n        \"baseAmount\": 100,\n        \"payment\": {\n            \"0_30_days\": 80,\n            \"31_60_days\": 50,\n            \"61_90_days\": 30,\n            \"91_120_days\": 10,\n            \"120_more_days\": 10\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/rewardProgram/calculator/sap"}, "response": []}, {"name": "Check Points Thought SAP", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/tenant-portal/rewardProgram/calculator/sap?customerExternalId=C20311&date=20240125&membership=CLASSIC&tenantId=1241&configurations.base_url=https://www.google.com/&configurations.username=admin&configurations.password=12345", "host": ["{{base_url}}"], "path": ["tenant-portal", "rewardProgram", "calculator", "sap"], "query": [{"key": "customerExternalId", "value": "C20311"}, {"key": "date", "value": "20240125", "description": "YYYYMMDD"}, {"key": "membership", "value": "CLASSIC", "description": "CLASSIC,VIP"}, {"key": "tenantId", "value": "1241"}, {"key": "configurations.base_url", "value": "https://www.google.com/"}, {"key": "configurations.username", "value": "admin"}, {"key": "configurations.password", "value": "12345"}]}}, "response": []}]}, {"name": "Program", "item": [{"name": "Create Reward Program", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": {{tenantId}},\n    \"isActive\": true,\n    \"name\": \"Reward Program\",\n    \"secondaryLanguageName\": \"Reward Program\",\n    \"milestones\": [\n        {\n            \"coinsRequired\": 1000,\n            \"bonusCoins\": 100\n        },\n        {\n            \"coinsRequired\": 2000,\n            \"bonusCoins\": 200\n        },\n        {\n            \"coinsRequired\": 3000,\n            \"bonusCoins\": 500\n        }\n    ],\n    \"vipRules\": {\n        \"upgradePoints\": 200,\n        \"renewPoints\": 150\n    },\n    \"baseAmount\": \"100\",\n    \"classicMemberCoinRules\": {\n        \"invoice\": {\n            \"normal\": 20,\n            \"cancellation\": -20\n        },\n        \"payment\": {\n            \"0_30_days\": 80,\n            \"31_60_days\": 50,\n            \"61_90_days\": 30,\n            \"91_120_days\": 10,\n            \"120_days_above\": 5\n        },\n        \"creditNotes\": {\n            \"returns\": -50,\n            \"discounts\": -20\n        },\n        \"actionTypes\": {\n            \"dailyAccess\": 20,\n            \"memberScan\": {\n                \"scanDurationLimit\": 20,\n                \"coins\": 20\n            }\n        }\n    },\n    \"vipMemberCoinRules\": {\n        \"invoice\": {\n            \"normal\": 24,\n            \"cancellation\": -24\n        },\n        \"payment\": {\n            \"0_30_days\": 96,\n            \"31_60_days\": 60,\n            \"61_90_days\": 36,\n            \"91_120_days\": 12,\n            \"120_days_above\": 6\n        },\n        \"creditNotes\": {\n            \"returns\": -50,\n            \"discounts\": -20\n        },\n        \"actionTypes\": {\n            \"dailyAccess\": 20,\n            \"memberScan\": {\n                \"scanDurationLimit\": 20,\n                \"coins\": 20\n            }\n        }\n    },\n    \"vipPointsRules\": {\n        \"payment\": {\n            \"0_30_days\": 5,\n            \"31_60_days\": 4,\n            \"61_90_days\": 2,\n            \"91_120_days\": 1,\n            \"120_days_above\": 1\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/rewardProgram/"}, "response": []}, {"name": "Update Reward Program", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"rewardProgramId\": \"65c0c245664bec3b14b9e5b3\",\n    \"isActive\": false,\n    \"name\": \"Reward Programs\",\n    \"secondaryLanguageName\": \"Reward Program\",\n    \"milestones\": [\n        {\n            \"coinsRequired\": 1000,\n            \"bouncePoints\": 100\n        },\n        {\n            \"coinsRequired\": 2000,\n            \"bouncePoints\": 200\n        },\n        {\n            \"coinsRequired\": 3000,\n            \"bouncePoints\": 500\n        }\n    ],\n    \"vipRules\": {\n        \"upgradePoints\": 200,\n        \"renewPoints\": 150\n    },\n    \"baseAmount\": \"100\",\n    \"classicMemberCoinRules\": {\n        \"invoice\": {\n            \"normal\": 20,\n            \"cancellation\": -20\n        },\n        \"payment\": {\n            \"0_30_days\": 80,\n            \"31_60_days\": 50,\n            \"61_90_days\": 30,\n            \"91_120_days\": 10,\n            \"120_days_above\": 5\n        },\n        \"creditNotes\": {\n            \"returns\": -50,\n            \"discounts\": -20\n        },\n        \"actionTypes\": {\n            \"dailyAccess\": 20,\n            \"memberScan\": {\n                \"scanDurationLimit\": 20,\n                \"coins\": 20\n            }\n        }\n    },\n    \"vipMemberCoinRules\": {\n        \"invoice\": {\n            \"normal\": 24,\n            \"cancellation\": -24\n        },\n        \"payment\": {\n            \"0_30_days\": 96,\n            \"31_60_days\": 60,\n            \"61_90_days\": 36,\n            \"91_120_days\": 12,\n            \"120_days_above\": 6\n        },\n        \"creditNotes\": {\n            \"returns\": -50,\n            \"discounts\": -20\n        },\n        \"actionTypes\": {\n            \"dailyAccess\": 20,\n            \"memberScan\": {\n                \"scanDurationLimit\": 20,\n                \"coins\": 20\n            }\n        }\n    },\n    \"vipPointsRules\": {\n        \"payment\": {\n            \"0_30_days\": 5,\n            \"31_60_days\": 4,\n            \"61_90_days\": 2,\n            \"91_120_days\": 1,\n            \"120_days_above\": 1\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/rewardProgram/"}, "response": []}, {"name": "List Reward Programs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/rewardProgram/?tenantId={{tenantId}}&page=1&perPage=20", "host": ["{{base_url}}"], "path": ["tenant-portal", "rewardProgram", ""], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "20"}, {"key": "projections[]", "value": "name", "disabled": true}, {"key": "projections[]", "value": "is_active", "disabled": true}]}}, "response": []}]}, {"name": "Configuration", "item": [{"name": "Update Reward Program Configuration", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": {{tenantId}},\n    \"baseAmount\": \"100\",\n    \"classicMemberCoinRules\": {\n        \"invoice\": {\n            \"normal\": 20,\n            \"cancellation\": -20\n        },\n        \"payment\": {\n            \"0_30_days\": 80,\n            \"31_60_days\": 50,\n            \"61_90_days\": 30,\n            \"91_120_days\": 10,\n            \"120_days_above\": 5\n        },\n        \"creditNotes\": {\n            \"returns\": -50,\n            \"discounts\": -20\n        },\n        \"actionTypes\": {\n            \"dailyAccess\": 20,\n            \"memberScan\": {\n                \"scanDurationLimit\": 20,\n                \"coins\": 20\n            }\n        }\n    },\n    \"vipMemberCoinRules\": {\n        \"invoice\": {\n            \"normal\": 24,\n            \"cancellation\": -24\n        },\n        \"payment\": {\n            \"0_30_days\": 96,\n            \"31_60_days\": 60,\n            \"61_90_days\": 36,\n            \"91_120_days\": 12,\n            \"120_days_above\": 6\n        },\n        \"creditNotes\": {\n            \"returns\": -50,\n            \"discounts\": -20\n        },\n        \"actionTypes\": {\n            \"dailyAccess\": 20,\n            \"memberScan\": {\n                \"scanDurationLimit\": 20,\n                \"coins\": 20\n            }\n        }\n    },\n    \"vipPointsRules\": {\n        \"payment\": {\n            \"0_30_days\": 5,\n            \"31_60_days\": 4,\n            \"61_90_days\": 2,\n            \"91_120_days\": 1,\n            \"120_days_above\": 1\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/rewardProgram/configuration"}, "response": []}, {"name": "Get Reward Program Configuration", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/rewardProgram/configuration?tenantId={{tenantId}}", "host": ["{{base_url}}"], "path": ["tenant-portal", "rewardProgram", "configuration"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "projections[]", "value": "name", "disabled": true}, {"key": "projections[]", "value": "is_active", "disabled": true}]}}, "response": []}]}, {"name": "Members", "item": [{"name": "Enrol Members In Reward Program", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}"}, {"key": "refreshToken", "value": "{{refreshToken}}"}, {"key": "userroleid", "value": "{{userRoleId}}"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": {{tenantId}},\n    \"customerUserRoleIds\": [\n       \"65263d91e018db00122863c8\",\n       \"655b12671eaff80012db433f\"\n    ],\n    \"effectiveDate\": \"2024/12/30\",\n    \"timzone\": \"Asia/Mumbai\",\n    \"customerPaymentTermInfo\": {\n        \"id\": \"65bb3005681802b2e9ca811e\",\n        \"numberOfDays\": 60\n    },\n    \"rewardProgramId\": \"6645e04d94fc9a0011a62592\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/rewardProgram/members"}, "response": []}, {"name": "Update Members Details of Reward Program", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": {{tenantId}},\n    \"customerUserRoleIds\": [\n        \"65263d91e018db00122863c8\"\n    ],\n    \"customerPaymentTermId\": \"65bb3005681802b2e9ca811f\",\n    \"rewardProgramId\": \"65b9f9b4dc667a1a3c23abed\",\n    \"restorePointsLevel\": true\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/rewardProgram/members"}, "response": []}, {"name": "List Members of Reward Program", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/tenant-portal/rewardProgram/members?tenantId={{tenantId}}&page=1&perPage=10", "host": ["{{base_url}}"], "path": ["tenant-portal", "rewardProgram", "members"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "search<PERSON>ey", "value": "100002", "disabled": true}]}}, "response": []}, {"name": "Members Details", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/tenant-portal/rewardProgram/member?tenantId={{tenantId}}&customerUserRoleId=655b12671eaff80012db433f", "host": ["{{base_url}}"], "path": ["tenant-portal", "rewardProgram", "member"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "customerUserRoleId", "value": "655b12671eaff80012db433f"}]}}, "response": []}, {"name": "Members Count", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/tenant-portal/rewardProgram/memberCount?tenantId={{tenantId}}&rewardProgramId=65e6ac5df5f0641b54d9e47a", "host": ["{{base_url}}"], "path": ["tenant-portal", "rewardProgram", "memberCount"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "rewardProgramId", "value": "65e6ac5df5f0641b54d9e47a"}]}}, "response": []}, {"name": "Remove Members From Reward Program", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/tenant-portal/rewardProgram/members?tenantId={{tenantId}}&rewardProgramId=65dec0ab329fb70d7a34bdce", "host": ["{{base_url}}"], "path": ["tenant-portal", "rewardProgram", "members"], "query": [{"key": "customerUserRoleIds[]", "value": "65263d91e018db00122863c8", "disabled": true}, {"key": "tenantId", "value": "{{tenantId}}"}, {"key": "rewardProgramId", "value": "65dec0ab329fb70d7a34bdce"}]}}, "response": []}]}, {"name": "Points", "item": [{"name": "List Point Logs", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/tenant-portal/rewardProgram/point?tenantId={{tenantId}}&rewardProgramId=65c0c245664bec3b14b9e5b3&rewardProgramMemberId=65c5aab4bc91db08ae8f32ec&page=1&perPage=10&usedForMobile=true", "host": ["{{base_url}}"], "path": ["tenant-portal", "rewardProgram", "point"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "rewardProgramId", "value": "65c0c245664bec3b14b9e5b3"}, {"key": "rewardProgramMemberId", "value": "65c5aab4bc91db08ae8f32ec"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "usedForMobile", "value": "true"}]}}, "response": []}, {"name": "Add Manual points", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": {{tenantId}},\n    \"rewardProgramId\" : \"65dec0ab329fb70d7a34bdce\",\n    \"rewardProgramMemberId\" : \"65e6f37c3d6dc1507518627a\",\n    \"customerUserRoleId\" : \"655c232cc140170012f656de\",\n    \"points\" : 1000,\n    \"pointType\": \"COINS\",\n    \"description\": \"Fix points\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/rewardProgram/point"}, "response": []}, {"name": "Daily Access", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": {{tenantId}},\n    \"customerUserRoleId\" : \"65263d91e018db00122863c8\",\n    \"timezone\": \"Asia/Kolkata\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/rewardProgram/dailyAccess"}, "response": []}, {"name": "Get QR Code For Member <PERSON>an", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/rewardProgram/memberScan?tenantId={{tenantId}}&customerUserRoleId=65263d91e018db00122863c8&rewardProgramId=6645e04d94fc9a0011a62592", "host": ["{{base_url}}"], "path": ["tenant-portal", "rewardProgram", "memberScan"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "customerUserRoleId", "value": "65263d91e018db00122863c8"}, {"key": "rewardProgramId", "value": "6645e04d94fc9a0011a62592"}]}}, "response": []}]}, {"name": "Product", "item": [{"name": "Create Reward Product", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": {{tenantId}},\n    \"name\": \"Reward 1\",\n    \"secondaryLanguageName\": \"Reward 1\",\n    \"productVariantId\": \"645501bb6abbc8e1140b0c0d\",\n    \"itemNumber\": \"rw_1\",\n    \"requiredCoins\": 1,\n    \"inventory\": 10,\n    \"tags\": [\n        \"Garage\"\n    ],\n    \"type\": \"FROM_PRODUCT\",\n    \"isFeatured\": true,\n    \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/rewardProgram/product"}, "response": []}, {"name": "Update Reward Product", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": {{tenantId}},\n    \"name\": \"Reward 1\",\n    \"secondaryLanguageName\": \"Reward 1\",\n    \"productVariantId\": \"645501bb6abbc8e1140b0c0d\",\n    \"itemNumber\": \"rw_1\",\n    \"requiredCoins\": 1,\n    \"inventory\": 10,\n    \"tags\": [\n        \"Garage\"\n    ],\n    \"type\": \"FROM_PRODUCT\",\n    \"isFeatured\": true,\n    \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/rewardProgram/product"}, "response": []}, {"name": "List Reward Product", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/tenant-portal/rewardProgram/product?tenantId={{tenantId}}&page=1&perPage=10", "host": ["{{base_url}}"], "path": ["tenant-portal", "rewardProgram", "product"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "search<PERSON>ey", "value": "", "disabled": true}, {"key": "onlyFeatured", "value": "true", "disabled": true}, {"key": "tagIds[]", "value": "65dec3d308d20b0f745b575c", "disabled": true}, {"key": "availableCoins", "value": null, "disabled": true}]}}, "response": []}, {"name": "List Reward Product Tags", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/tenant-portal/rewardProgram/productTags?tenantId={{tenantId}}&page=1&perPage=10", "host": ["{{base_url}}"], "path": ["tenant-portal", "rewardProgram", "productTags"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "Update Reward Products", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": {{tenantId}},\n    \"isActive\": true,\n    \"rewardProductIds\": [\n        \"6603e11cad31814ffc6730a5\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/rewardProgram/product"}, "response": []}, {"name": "Delete Reward Products", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/tenant-portal/rewardProgram/product?tenantId={{tenantId}}", "host": ["{{base_url}}"], "path": ["tenant-portal", "rewardProgram", "product"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "rewardProductIds[]", "value": "6603e11cad31814ffc6730a5", "disabled": true}]}}, "response": []}]}, {"name": "Product Claims", "item": [{"name": "C<PERSON>m Reward Product", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": 1241,\n    \"rewardProductId\": \"67c046f23d1970d1bfcd60cd\",\n    \"rewardProgramId\": \"67b40beff475195453ac99f3\",\n    \"rewardProgramMemberId\": \"67bbfba65ffc8ff62b1d1767\",\n    \"customerUserRoleId\": \"655b12671eaff80012db433f\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/rewardProgram/productClaims"}, "response": []}, {"name": "Update Claimed Reward Product Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": {{tenantId}},\n    \"status\": \"PENDING\",\n    \"claimIds\": [\n        \"65deeff0ece2e531aaf9309f\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/rewardProgram/productClaims"}, "response": []}, {"name": "List Reward Product Claims", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/tenant-portal/rewardProgram/productClaims?tenantId={{tenantId}}&page=1&perPage=10&claimStatus=PROCESSING", "host": ["{{base_url}}"], "path": ["tenant-portal", "rewardProgram", "productClaims"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "claimStatus", "value": "PROCESSING"}]}}, "response": []}]}, {"name": "Dashboard", "item": [{"name": "Dashboard Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}"}, {"key": "refreshToken", "value": "{{refreshToken}}"}, {"key": "userroleid", "value": "{{userRoleId}}"}], "url": {"raw": "{{base_url}}/tenant-portal/rewardProgram/pointsSummary?tenantId=1114&year=2024&pointType=COINS&timezone=Asia/Kolkata", "host": ["{{base_url}}"], "path": ["tenant-portal", "rewardProgram", "pointsSummary"], "query": [{"key": "tenantId", "value": "1114"}, {"key": "rewardProgramId", "value": "662f411a064b8b00121df9c6", "disabled": true}, {"key": "year", "value": "2024"}, {"key": "pointType", "value": "COINS"}, {"key": "timezone", "value": "Asia/Kolkata"}]}}, "response": []}, {"name": "Dashboard Historic Distribution", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/rewardProgram/pointsHistoricDistribution?tenantId={{tenantId}}&rewardProgramId=65dec0ab329fb70d7a34bdce&pointType=COINS&durationPeriod=MONTH&timezone=Asia/Kolkata", "host": ["{{base_url}}"], "path": ["tenant-portal", "rewardProgram", "pointsHistoricDistribution"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "rewardProgramId", "value": "65dec0ab329fb70d7a34bdce"}, {"key": "pointType", "value": "COINS"}, {"key": "durationPeriod", "value": "MONTH"}, {"key": "timezone", "value": "Asia/Kolkata"}]}}, "response": []}]}]}, {"name": "Notification", "item": [{"name": "Schedule", "item": [{"name": "Schedule Notification", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}"}, {"key": "refreshToken", "value": "{{refreshToken}}"}, {"key": "userroleid", "value": "{{userRoleId}}"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": {{tenantId}},\n    \"title\": \"Deals\",\n    \"message\": \"New deals commming\",\n    \"scheduleAt\": \"2022-09-09T00:00:00\",\n    \"timezone\": \"Asia/Kolkata\",\n    \"data\": {\n        \"tenantId\": 1241,\n        \"dealId\": \"6630889f9958d544b8ab14c5\"\n    },\n    \"notificationType\": \"DEALS_UPDATES\",\n    \"priceListIds\": [\n        \"65263d3ed5e2ed3184c7b894\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/schedule-notification"}, "response": []}, {"name": "Update Schedule Notification", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{Authorization}}"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}"}, {"key": "refreshToken", "value": "{{refreshToken}}"}, {"key": "userroleid", "value": "{{userRoleId}}"}], "body": {"mode": "raw", "raw": "{\n    \"scheduledNotificationId\": \"6641a7cff0c2552f8f6dcd8c\",\n    \"tenantId\": {{tenantId}},\n    \"title\": \"Deals\",\n    \"message\": \"New deals commming\",\n    \"scheduleAt\": \"2022-09-07T00:00:00\",\n    \"timezone\": \"Asia/Kolkata\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/schedule-notification"}, "response": []}]}, {"name": "List Notifications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/notifications/?tenantId={{tenantId}}&page=1&perPage=1&userRoleId=6512bb311b4b3b0012c4e2dd", "host": ["{{base_url}}"], "path": ["tenant-portal", "notifications", ""], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "1"}, {"key": "userRoleId", "value": "6512bb311b4b3b0012c4e2dd"}, {"key": "", "value": "", "disabled": true}]}}, "response": []}]}, {"name": "New Folder", "item": []}, {"name": "Scan QR Code", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshToken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": {{tenantId}},\n    \"customerUserRoleId\": \"65263d91e018db00122863c8\",\n    \"qrInfo\": \"3kwVQ//ulKXY8KG3:4IpM2UlM9pa+RAs7DWcfpkvAcN6D5lGT9R+vPOdvh0lD790Ol9oB7ngwJeUrbmBaKwlL7jTdM7eTC7fQhKGABHnem0pYUljAuAKvPgQF1ERUsG1165wfCa0Z4BksQInCnb0rw8r67srntzaqfzs/og==:DJItfosXDRf5qTlLd66M7Q==\",\n    \"timezone\": \"Asia/Kolkata\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/tenant-portal/scanQrCode"}, "response": []}, {"name": "List of each-other linked tenants", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "devicetoken", "value": "{{<PERSON><PERSON>en}}", "type": "text"}, {"key": "refreshtoken", "value": "{{refreshToken}}", "type": "text"}, {"key": "userroleid", "value": "{{userRoleId}}", "type": "text"}], "url": {"raw": "{{base_url}}/tenant-portal/eachOtherLinkedTenants?tenantId=1114", "host": ["{{base_url}}"], "path": ["tenant-portal", "eachOtherLinkedTenants"], "query": [{"key": "tenantId", "value": "1114"}]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "https://rukx2brvl9.execute-api.me-south-1.amazonaws.com/development"}, {"key": "app_session", "value": "65268406be8230bc327b387b", "type": "string"}, {"key": "local_base_url", "value": "localhost:3520", "type": "string", "disabled": true}, {"key": "staging_base_url", "value": "https://jxdyvef8c6.execute-api.me-south-1.amazonaws.com/staging", "type": "string", "disabled": true}, {"key": "production_base_url", "value": "https://hxfvnwimb6.execute-api.me-south-1.amazonaws.com/production", "type": "string", "disabled": true}, {"value": "", "type": "string", "disabled": true}]}