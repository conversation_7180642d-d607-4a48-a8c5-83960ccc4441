const jwkToPem = require('jwk-to-pem');
const jwt = require('jsonwebtoken');

const { validationResult } = require('express-validator');

const {
    users: UserSchema,
    user_sessions: UserSessionSchema,
    user_roles: UserRoleSchema,
    tenant_customers: TenantCustomerSchema,
    tenants: TenantSchema,
} = require('../Database/Schemas');

const UserAuthModal = new (require("../Models/auth"))();
const sendFailureEmail = require('./SendFailureEmail').default

const {
    VALUES,
    deviceTypeDetected,
    PRIMITIVE_ROLES,
    TENANT_SERVICES,
} = require('../Configs/constants');

exports.authentication = async (request, response, next) => {
    const errors = validationResult(request).formatWith(({ msg }) => msg)

    if (!errors.isEmpty()) {
        await sendFailureEmail(request, errors.array())

        return response.handler.badRequest(
            undefined,
            errors.array()
        );
    }

    // USED FOR WHEN AUTHENTICATION IS OPTIONAL
    if (!request.headers.authorization
        || request.originalUrl.includes('/auth/app-sign-in')
        || request.originalUrl.includes('/auth/sign-in')
        || request.originalUrl.includes('/auth/verify-auth-otp')
    ) {
        return next();
    }

    // TO discard saving the refreshed-access-token in the browser (browser by default saves the response headers). if needed add this header to non protected routs too.
    // response.setHeader("Cache-Control", "max-age=0");
    response.setHeader("Cache-Control", "no-cache");

    const userRoleDetails = await UserRoleSchema
        .findById(request.headers.userroleid)
        .populate({ path: "role_id" })
        .populate({ path: "user_id" })
        .populate({ path: "tenant_id", select: 'services' });

    const logErrorData = (data = {}) => {
        const logData = { ...data }

        const userRoleInfo =
            data.userRoleDetails ||
            request.headers.sessionDetails ||
            userRoleDetails

        if (userRoleInfo) {
            logData["customer_name"] = userRoleInfo.user_id?.customer_name || userRoleInfo.user_id?.customer_legal_name
            logData["customer_email"] = userRoleInfo.user_id?.customer_email
            logData["role_id"] = {
                "_id": userRoleInfo.role_id?._id,
                "name": userRoleInfo.role_id?.name,
            }
            logData["last_mobile_login_time"] = userRoleInfo.last_mobile_login_time
            logData["last_tablet_login_time"] = userRoleInfo.last_tablet_login_time
        }
        return logData
    }

    if (request.headers.userroleid) {
        let message;

        if (!userRoleDetails) {
            message = "validation_not_found_role";
        }
        else if (userRoleDetails && !userRoleDetails.is_active) {
            message = "profile_is_inactive";
        }
        else if (userRoleDetails && userRoleDetails.is_deleted) {
            message = "profile_is_deleted";
        }
        else if (userRoleDetails.role_id.name === PRIMITIVE_ROLES.CUSTOMER) {
            const {
                customer_app_access,
                tenant_id,
                device_access = [],
            } = userRoleDetails

            // Validates if customer still holds app access or not
            if (customer_app_access) {
                const tenantServices = await TenantSchema.findOne(
                    { _id: tenant_id },
                    { services: 1 },
                    { lean: true }
                );

                if (!tenantServices.services?.find(s => s.key === TENANT_SERVICES.CUSTOMER_APP)?.permission?.view) {
                    message = 'tenant_app_access_not_active'
                }
            }
            else {
                message = "app_access_not_active";
            }

            /**
             * @variation Below routes excluded as from mobile unwanted userRoleId is coming.
             * Mobile team has to fix it then we can remove below conditions
             */
            if (
                !request.originalUrl.includes("/auth/app-user-roles") &&
                !request.originalUrl.includes("/auth/user-role-accessed")
            ) {
                // Validate if customer still holds valid recognized device or not
                const deviceErrorMessage = "device_no_longer_recognized"

                if (device_access.length) {
                    const deviceId = request.headers.devicetoken
                    const deviceType = request.headers.deviceaccesstype
                    const deviceOs = request.headers.devicetype

                    const hasValidDevice = device_access.find(device =>
                        device.device_id === deviceId &&
                        device.type === deviceType &&
                        device.os === deviceOs
                    )

                    if (!hasValidDevice) {
                        message = deviceErrorMessage
                    }
                }
                else {
                    message = deviceErrorMessage
                }
            }
        }
        if (message) {
            const data = logErrorData()
            return response.handler.unauthorized(message, data);
        }
    }
    const portal_type = userRoleDetails?.role_id.portal_type

    request.headers.sessionDetails = userRoleDetails;
    request.headers.portalType = portal_type;
    request.headers.tenantDetail = userRoleDetails?.tenant_id;

    let checkCognitoToken = false;
    request.headers.deviceTypeDetected = deviceTypeDetected.APPLICATION;

    try {
        const {
            user,
            tenant_customer,
            deviceaccesstype,
            login_time,
        } = jwt.verify(request.headers.authorization, process.env.JWT_TOKEN_SECRET);

        if (
            [
                VALUES.portals.SALES_APP,
                VALUES.portals.TENANT_PORTAL,
                VALUES.portals.SUPERVISOR_APP,
            ].includes(portal_type)
        ) {
            if (
                request.headers.userroleid &&
                (!user || (userRoleDetails.user_id.id !== user._id))
            ) {
                const errorMessage = "invalid_user_id"
                const data = logErrorData({ user })
                const customError = new Error(errorMessage)

                return response.handler.unauthorized(errorMessage, data, customError);
            }
            request.headers.userDetails = await UserSchema.findById(user._id)
        }
        else {
            if (
                request.headers.userroleid &&
                (!tenant_customer || (userRoleDetails.user_id.id !== tenant_customer._id))
            ) {
                const errorMessage = "invalid_user_id"
                const data = logErrorData({ tenant_customer })
                const customError = new Error(errorMessage)

                return response.handler.unauthorized(errorMessage, data, customError);
            }
            request.headers.userDetails = await TenantCustomerSchema.findById(tenant_customer)
        }

        // DO NOT CHECK FOR ('/auth/user-role-accessed')
        if (
            (deviceaccesstype === VALUES.deviceAccessType.MOBILE) // TODO: need to remove this check if in future, we need to maintain single device sign in for TABLET too
            && (portal_type === VALUES.portals.SALES_APP) // TODO: need to remove this check if in future, we need to maintain single device sign in for all roles of tenant
            && (!request.originalUrl.includes("/auth/user-role-accessed"))
            && (!request.originalUrl.includes('/auth/app-user-roles'))
        ) {
            const tokenLoginTime = moment(login_time);
            if (!tokenLoginTime.isValid()) {
                const errorMessage = "invalid_token_found"

                const data = logErrorData({
                    tokenLoginTime,
                    login_time,
                })
                const customError = new Error(errorMessage)

                return response.handler.unauthorized(errorMessage, data, customError);
            }

            const dbLoginTime = deviceaccesstype === VALUES.deviceAccessType.MOBILE
                ? userRoleDetails?.last_mobile_login_time
                : userRoleDetails?.last_tablet_login_time;

            if ((moment(dbLoginTime).toISOString() !== login_time) || (!dbLoginTime)) { // check for latest session time and token's login time
                const errorMessage = "invalid_session"

                const data = logErrorData({
                    dbLoginTime,
                    login_time,
                })
                const customError = new Error(errorMessage)

                return response.handler.unauthorized(errorMessage, data, customError);
            }
        }
    }
    catch (error) {
        switch (error.name) {
            case "TokenExpiredError":
                {
                    try {
                        const { login_time, deviceaccesstype } = jwt.decode(request.headers.authorization);

                        let loginTime;
                        let lastLoginDbField;

                        if (deviceaccesstype === VALUES.deviceAccessType.MOBILE) {
                            lastLoginDbField = 'last_mobile_login_time';
                        }
                        else {
                            lastLoginDbField = 'last_tablet_login_time';
                        }
                        loginTime = userRoleDetails[lastLoginDbField];

                        if (
                            (deviceaccesstype === VALUES.deviceAccessType.MOBILE) // TODO: need to remove this check if in future, we need to maintain single device sign in for TABLET too
                            && (portal_type === VALUES.portals.SALES_APP) // TODO: need to remove this check if in future, we need to maintain single device sign in for all roles of tenant
                        ) {
                            if (!login_time || !loginTime) {
                                const errorMessage = "token_login_time_not_found"

                                const data = logErrorData({
                                    login_time_from_token: login_time,
                                    last_device_login_time_from_db: loginTime,
                                })
                                const customError = new Error(errorMessage)

                                return response.handler.unauthorized(errorMessage, data, customError);
                            }

                            if (!deviceaccesstype || ((deviceaccesstype !== VALUES.deviceAccessType.MOBILE) && (deviceaccesstype !== VALUES.deviceAccessType.TABLET))) {
                                const errorMessage = "deviceaccesstype_not_found"
                                const data = logErrorData({ deviceaccesstype })
                                const customError = new Error(errorMessage)

                                return response.handler.unauthorized(errorMessage, data, customError);
                            }

                            if (loginTime.toISOString() !== login_time) {
                                const errorMessage = "invalid_session"

                                const data = logErrorData({
                                    login_time_from_token: login_time,
                                    last_device_login_time_from_db: loginTime.toISOString(),
                                })
                                const customError = new Error(errorMessage)

                                return response.handler.unauthorized(errorMessage, data, customError);
                            }
                        }
                        else if (portal_type !== VALUES.portals.SALES_APP && !loginTime) { // if some user do not have last_login_time in db, then we will update the last_login_time in db and generate new token with the same time
                            // this case is used for users who are not salesperson and their session started before single device sing in feature deployment
                            loginTime = new Date();

                            await UserRoleSchema.updateOne(
                                { _id: userRoleDetails._id },
                                { [lastLoginDbField]: loginTime }
                            );
                        }

                        let sessionData = await UserSessionSchema.findOne(
                            {
                                status: VALUES.sessionStatus.ACTIVE,
                                access_token: request.headers.authorization,
                            }
                        );

                        if (sessionData) {
                            sessionData.status = VALUES.sessionStatus.EXPIRED;
                            await sessionData.save()
                        }

                        if (!userRoleDetails?.user_id) {
                            const errorMessage = "invalid_user_id"
                            const data = logErrorData()
                            const customError = new Error(errorMessage)

                            return response.handler.unauthorized(errorMessage, data, customError);
                        }

                        if (sessionData && sessionData.user_id.toString() !== userRoleDetails.user_id.id) {
                            sessionData.status = VALUES.sessionStatus.CLOSE;
                            await sessionData.save()

                            const errorMessage = "invalid_user_id"
                            const data = logErrorData({ sessionData })
                            const customError = new Error(errorMessage)

                            return response.handler.unauthorized(errorMessage, data, customError);
                        }

                        request.headers.userDetails = userRoleDetails.user_id;
                        userRoleDetails.depopulate("user_id");

                        const token = await UserAuthModal.generateAppAccessTokenFromRefreshToken(
                            request.headers.refreshtoken,
                            userRoleDetails,
                            loginTime,
                            deviceaccesstype,
                        );

                        const { user, tenant_customer } = jwt.verify(token, process.env.JWT_TOKEN_SECRET)

                        const userDetails = (
                            [
                                VALUES.portals.SALES_APP,
                                VALUES.portals.TENANT_PORTAL,
                                VALUES.portals.SUPERVISOR_APP
                            ].includes(portal_type)
                        )
                            ? user
                            : tenant_customer;

                        if (!userDetails) {
                            const errorMessage = "valid_user_not_found"

                            const data = logErrorData({
                                user,
                                tenant_customer,
                            })
                            const customError = new Error(errorMessage)

                            return response.handler.unauthorized(errorMessage, data, customError);
                        }

                        request.headers.updatedAccessToken = token;
                        response.setHeader("refreshed-access-token", token);

                        let newSession = new UserSessionSchema({
                            access_token: token,
                            status: VALUES.sessionStatus.ACTIVE,
                            total_session_time_in_sec: process.env.JWT_EXPIRE_IN_HOURS.split('h')[0] * 3600000,
                            user_id: userDetails._id,
                            device_token: request.headers.devicetoken,
                            device_type: request.headers.devicetype,
                            start_time: new Date(),
                            user_role_id: userRoleDetails._id,
                            tenant_id: userRoleDetails.tenant_id?._id,
                            collection_name: userRoleDetails.collection_name
                        });
                        await newSession.save();
                    }
                    catch (error) {
                        let errorMessage = error.message
                        const data = logErrorData()

                        switch (error.name) {
                            case "TokenExpiredError": {
                                errorMessage = "refresh_token_expired"
                                return response.handler.unauthorized(errorMessage, data, error);
                            }

                            case "invalid_user_id": {
                                errorMessage = "invalid_user_id"
                                return response.handler.unauthorized(errorMessage, data, error);
                            }

                            default: {
                                errorMessage = "token_regeneration_error"
                                return response.handler.unauthorized(errorMessage, data, error);
                            }
                        }
                    }
                    break;
                }

            case "JsonWebTokenError": {
                if (error.message === "invalid algorithm") {
                    checkCognitoToken = true;
                    break;
                }
                const errorMessage = "invalid_token"
                const data = logErrorData()
                return response.handler.unauthorized(errorMessage, data, error);
            }

            default:
                response.handler.serverError(error);
                break;
        }
    }

    if (checkCognitoToken) {
        request.headers.deviceTypeDetected = deviceTypeDetected.PORTAL;

        const jwkRes = await fetch(`https://cognito-idp.${VALUES.awsUserPoolRegion}.amazonaws.com/${VALUES.userPoolData.UserPoolId}/.well-known/jwks.json`)

        if (!jwkRes.ok) {
            return response.handler.unauthorized("VALIDATION.NOT_FOUND.COULD_NOT_FETCH");
        }
        let { keys } = await jwkRes.json();
        const pems = {}

        for (var i = 0; i < keys.length; i++) {
            //Convert each key to PEM
            let key_id = keys[i].kid;
            let modulus = keys[i].n;
            let exponent = keys[i].e;
            let key_type = keys[i].kty;
            let jwk = { kty: key_type, n: modulus, e: exponent };
            let pem = jwkToPem(jwk);
            pems[key_id] = pem;
        }
        let decodedJwt = jwt.decode(request.headers.authorization, { complete: true });

        if (!decodedJwt) {
            return response.handler.unauthorized("can_not_find_decodedJwt");
        }

        var kid = decodedJwt.header.kid;
        var pem = pems[kid];
        if (!pem) {
            return response.handler.unauthorized("can_not_find_pem");;
        }

        try {
            const payload = jwt.verify(request.headers.authorization, pem);
            request.headers.payload = payload;

            const [userDetails] = await Promise.all(
                [
                    UserSchema.findOne({ cognito_username: payload.sub }),
                    // UserRoleSchema.findById(request.headers.userroleid).populate({ path: "role_id" })
                ]);

            if (!userDetails) {
                return response.handler.notFound("valid_user_not_found");
            }
            request.headers.userDetails = userDetails;

        }
        catch (error) {
            switch (error.name) {
                case "TokenExpiredError":
                    let sessionData = await UserSessionSchema.findOne(
                        {
                            status: VALUES.sessionStatus.ACTIVE,
                            access_token: request.headers.authorization
                        }
                    );

                    const userRoleDetails = await UserRoleSchema
                        .findById(request.headers.userroleid)
                        .populate({ path: "role_id" })
                        .populate({ path: "user_id" })
                        .populate({ path: "tenant_id", select: 'services' });

                    if (
                        !userRoleDetails ||
                        !userRoleDetails.is_active ||
                        userRoleDetails.is_deleted
                    ) {
                        return response.handler.unauthorized("can't_generate_new_token")
                    }

                    if (sessionData && sessionData.user_id.toString() !== userRoleDetails.user_id.id) {
                        const errorMessage = "invalid_user_id"
                        const data = logErrorData({
                            sessionData,
                            userRoleDetails,
                        })
                        const customError = new Error(errorMessage)

                        return response.handler.unauthorized(errorMessage, data, customError);
                    }

                    request.headers.sessionDetails = userRoleDetails;
                    request.headers.userDetails = userRoleDetails.user_id;
                    request.headers.tenantDetail = userRoleDetails.tenant_id;

                    const portal_type = userRoleDetails.role_id.portal_type;
                    request.headers.portalType = portal_type;

                    if (portal_type === VALUES.portals.SYSTEM_PORTAL) {
                        if (sessionData) {
                            sessionData.status = VALUES.sessionStatus.EXPIRED;
                            sessionData.end_time = new Date();
                            await sessionData.save();
                        }
                        return response.handler.unauthorized("VALIDATION.EXPIRED.TOKEN", { type: "TOKEN_EXPIRED" });
                    }
                    else if (
                        [
                            VALUES.portals.TENANT_PORTAL,
                            VALUES.portals.BRANCH_PORTAL,
                        ].includes(portal_type)
                    ) {
                        if (!request.headers.refreshtoken) {
                            if (sessionData) {
                                sessionData.status = VALUES.sessionStatus.EXPIRED;
                                sessionData.end_time = new Date();
                                await sessionData.save();
                            }
                            return response.handler.unauthorized("validation_not_found_valid_refresh_token")
                        }

                        try {
                            const tokens = await UserAuthModal.generateTokenFromRefreshToken(request.headers.refreshtoken);
                            const accessToken = tokens.AuthenticationResult?.AccessToken;
                            if (!accessToken) {
                                return response.handler.unauthorized("validation_not_found_valid_refresh_token")
                            }

                            //check that the refresh token belonged to original user....
                            const payload = jwt.verify(accessToken, pem);
                            request.headers.payload = payload;

                            const userDetails = await UserSchema.findOne({ cognito_username: payload.sub });
                            if (userDetails.id !== userRoleDetails.user_id.id) {
                                const errorMessage = "invalid_user_id"
                                const data = logErrorData({ userDetails })
                                const customError = new Error(errorMessage)

                                return response.handler.unauthorized(errorMessage, data, customError);
                            }

                            request.headers.updatedAccessToken = accessToken;
                            response.setHeader("refreshed-access-token", accessToken);

                            if (sessionData) {
                                sessionData.status = VALUES.sessionStatus.EXPIRED;
                                await sessionData.save();
                            }

                            let newSession = new UserSessionSchema({
                                access_token: accessToken,
                                status: VALUES.sessionStatus.ACTIVE,
                                total_session_time_in_sec: tokens.AuthenticationResult.ExpiresIn,
                                user_id: userDetails._id,
                                device_token: request.headers.devicetoken,
                                device_type: request.headers.devicetype,
                                start_time: new Date(),
                                user_role_id: userRoleDetails._id,
                                tenant_id: userRoleDetails.tenant_id?._id,
                                collection_name: userRoleDetails.collection_name,
                            });
                            await newSession.save();
                        }
                        catch (error) {
                            if (sessionData) {
                                sessionData.status = VALUES.sessionStatus.EXPIRED;
                                sessionData.end_time = new Date();
                                await sessionData.save();
                            }
                            return response.handler.unauthorized("validation_not_found_valid_refresh_token")
                        }
                    }
                    else {
                        if (sessionData) {
                            sessionData.status = VALUES.sessionStatus.CLOSE;
                            sessionData.end_time = new Date();
                            await sessionData.save();
                        }
                        return response.handler.unauthorized("validation_not_found_valid_session_details");
                    }
                    break;

                case "JsonWebTokenError": {
                    return response.handler.unauthorized("invalid_signature");
                }

                default: {
                    return response.handler.unauthorized(error.message);
                }
            }
        }
    }

    if (
        request.headers.userroleid &&
        (!userRoleDetails || userRoleDetails.is_deleted)
    ) {
        const errorMessage = "invalid_user_role_id"
        const data = logErrorData()
        const customError = new Error(errorMessage)

        return response.handler.unauthorized(errorMessage, data, customError);
    }

    if (
        request.headers.userroleid &&
        request.headers.userDetails.id !== userRoleDetails.user_id._id.toString()
    ) {
        const errorMessage = "invalid_user_id"
        const data = logErrorData()
        const customError = new Error(errorMessage)

        return response.handler.unauthorized(errorMessage, data, customError);
    }

    next();
};
