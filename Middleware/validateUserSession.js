const { VALUES, deviceTypeDetected } = require('../Configs/constants');
const { users: UserSchema, user_sessions: UserSessionSchema, user_devices: UserDeviceSchema } = require('../Database/Schemas');

exports.validateUserDevice = async (request, response, next) => {
    const portalType = request.headers.portalType;
    if (request.headers.deviceTypeDetected === deviceTypeDetected.APPLICATION) {
        return next();
    } else {
        const userSession = await UserDeviceSchema.findOne({
            device_token: request.headers.devicetoken,
            user_id: request.headers.userDetails?._id
        });
    
        if(!userSession) {
            return response.handler.unauthorized("validation_not_found_could_not_find_valid_session");
        }
        if(!userSession.device_verified) {
            return response.handler.unauthorized("validation.not_found.validate_email_or_mobile_for_session")
        }
    
        next();
    }
}