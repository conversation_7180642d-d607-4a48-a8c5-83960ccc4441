const {
    header,
    body,
    query,
} = require('express-validator');

const validator = require("validator")

const path = require("path");

const {
    VALUES,
    LISTING_TYPES,
    REGEX,
    ENTITY_STATUS,
    CUSTOMER_STATUS_TYPE,
    <PERSON><PERSON>CH_LIST_TYPE,
    CUSTOMER_TYPE,
    CUSTOMER_PAYMENT_FILTER_TYPE,
    CUSTOMER_REWARD_PROGRAM_FILTER_TYPE,
    TENANT_ALLOW_VALIDATION,
    DATA_SHEET,
    VALID_IMAGES_EXTS,
    TENANT_USER_SORT_BY,
    SORT_TYPE
} = require('../../Configs/constants');

const {
    headerValidator,
    tenantIdValidator,
    isActiveValidator,
    tenantIdBodyValidator,
    idsValidator,
    tenantIdQueryValidator,
    perPageValidator,
    pageValidator,
    searchKeyValidator,
    userRoleIdQueryValidator,
    customerUserRoleIdBodyValidator,
    timezoneBodyValidator,
    projectionQueryValidator,
    statusBodyValidator,
} = require('./CommonValidator');

exports.addTenantBranchPortalUser = [
    header('devicetoken', 'deviceToken field is required').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("portalType").isIn(Object.values(VALUES.portals)).withMessage(`Please provide portal type as in ${Object.values(VALUES.portals).join(", ")}`),
    body("isActive", "Please provide is active").trim().notEmpty().custom((value, { req, path, location }) => {
        if (value === "true") {
            req.body.isActive = true;
            return true
        } else if (value === "false") {
            req.body.isActive = false;
            return true;
        } else {
            return false;
        }
    }).withMessage("Please send isActive status in true or false value only"),
    body("allowPriceChange", "Please provide is allowPriceChange").trim().notEmpty().custom((value, { req, path, location }) => {
        if (value === "true") {
            req.body.allowPriceChange = true;
            return true
        } else if (value === "false") {
            req.body.allowPriceChange = false;
            return true;
        } else {
            return false;
        }
    }).withMessage("Please send allowPriceChange status in true or false value only"),
    body("allowPostingVoucherReceipts", "Please provide is allowPostingVoucherReceipts").optional().isBoolean(),
    body("profilePic").optional().isArray({ min: 1 }),
    body("firstName", "Please provide first name").trim().notEmpty(),
    body("lastName", "Please provide last name").trim().notEmpty(),
    body("roleId", "Please provide role id").trim().notEmpty().isMongoId(),
    body("email", "Please provide email id").trim().notEmpty().isEmail(),
    body("mobileNumber", "Please provide mobile number").trim().notEmpty(),
    body("countryCode", "Please provide country code").custom((value, { req, path, location }) => {
        return REGEX.COUNTRY_CODE.test(value);
    }).withMessage("Pleas provide valid country code"),
    body("notifications").isArray({ min: 1 }).withMessage("Please send notifications"),
    body("tenantId").isInt({ min: 1000 }).withMessage("Please send valid tenant id"),
    body("branchId").optional().trim().isMongoId(),
    body("supervisorId").optional().trim().isMongoId(),
];

// exports.updateUserRoleValidator = [
//     ...headerValidator,
//     body("preferred_language", "Please provide preferred language").trim().notEmpty(),
// ]

exports.editTenantBranchUser = [
    header('devicetoken', 'deviceToken field is required').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("userRoleId", "Please provide user role id").trim().notEmpty().isMongoId(),
    body("portalType").isIn(Object.values(VALUES.portals)).withMessage(`Please provide portal type as in ${Object.values(VALUES.portals).join(", ")}`),
    body("isActive", "Please provide is active").isBoolean(),
    body("allowPriceChange", "Please provide is active").isBoolean(),
    body("allowPostingVoucherReceipts", "Please provide is allowPostingVoucherReceipts").optional().isBoolean(),
    body("firstName", "Please provide first name").trim().notEmpty(),
    body("lastName", "Please provide last name").trim().notEmpty(),
    body("roleId", "Please provide role id").trim().notEmpty().isMongoId(),
    body("email", "Please provide email id").trim().notEmpty().isEmail(),
    body("mobileNumber", "Please provide mobile number").trim().notEmpty(),
    body("countryCode", "Please provide country code").custom((value, { req, path, location }) => {
        return REGEX.COUNTRY_CODE.test(value);
    }).withMessage("Pleas provide valid country code"),
    body("notifications").isArray({ min: 1 }).withMessage("Please send notifications"),
    body("tenantId").isInt({ min: 1000 }).withMessage("Please send valid tenant id"),
    body("branchId").optional().trim().isMongoId(),
    body("supervisorId").optional().trim().isMongoId(),
    body("newSupervisorId").optional().isMongoId().withMessage("Please provide valid Supervisor Id"),
    body("newSalesPersonId").optional().isMongoId().withMessage("Please provide valid sales person id")
    // body("newSupervisorId").optional().trim().isMongoId()


]

exports.getTenantBranchPortalUser = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    query("userRoleId", "Please provide user role id").trim().notEmpty().isMongoId()
];

exports.getTenantSupervisors = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    query("tenantId", "Please provide tenant id").isInt({ min: 1000 }).withMessage("Please provide tenant valid"),
    // query("branchId", "Please provide branch id").trim().notEmpty().isMongoId(),
]

exports.getTenantSalesperson = [
    ...headerValidator,
    ...tenantIdQueryValidator,

    query("branchId", "Please provide branch id")
        .optional()
        .isMongoId(),

    query("isActive", "Please provide isActive")
        .optional()
        .isBoolean()
        .toBoolean(),
]

exports.usersDetails = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("tenantId", "Please provide valid tenant id").isInt({ min: 1000 }).toInt(),
    body("ids", "Please provide valid Ids").isArray()
]

exports.deleteTenantBranchPortalUser = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),

    query("userRoleId", "Please provide valid 'userRoleId'")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId(),

    query("newSupervisorId", "Please provide valid 'newSupervisorId'")
        .optional()
        .trim()
        .isMongoId()
];

exports.addCustomer = [
    header('devicetoken', 'deviceToken field is required').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("firstName", "Please provide first name").trim().notEmpty(),
    body("lastName", "Please provide last name").trim().notEmpty(),
    body("countryCode", "Please provide country code").custom((value, { req, path, location }) => {
        return REGEX.COUNTRY_CODE.test(value);
    }).withMessage("Pleas provide valid country code"),
    body("mobileNumber", "Please provide mobile number").trim().notEmpty(),
    body("email", "Please provide email id").optional().trim().notEmpty().isEmail(),
    body("customerAppAccess", "Please provide customerAppAccess").optional().isBoolean().toBoolean(),
    body("isActive", "Please provide is active").isBoolean(),
    // body("shippingAddress", "Please provide shipping address").optional().trim().notEmpty(),
    body("longitude", "Please provide longitude").trim().notEmpty(),
    body("latitude", "Please provide latitude").trim().notEmpty(),
    body("shippingCountryId", "Please provide shipping country id").trim().notEmpty(),
    body("shippingRegionId", "Please provide shipping region id").optional().trim().notEmpty(),
    body("shippingCityId", "Please provide shipping city id").optional().trim().notEmpty(),
    // body("shippingCountryCode", "Please provide shipping country code").optional().trim().notEmpty(),
    // body("shippingMobileNumber", "Please provide shipping mobile number").optional().trim().notEmpty(),
    // body("externalId", "Please provide external id").optional().trim().notEmpty(),
    body("accountNumber", "Please provide account number").optional().trim(),
    body("customerName", "Please provide customer name").trim().notEmpty(),
    body("legalName", "Please provide legal name").trim().notEmpty(),
    body("salesPersonId", "Please provide sales person id").trim().notEmpty(),
    body("priceListId", "Please provide price list id").trim().notEmpty(),
    body("customerAppRequest", "Please provide valid customer app request").optional().isBoolean(),
    body("customerCatalogMode", "Please provide valid customer catalog modal").optional().isBoolean(),
];

exports.getCustomer = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    query("userRoleId", "Please provide user role id").optional().isMongoId(),
    query("customerUserRoleIds", "Please provide valid customer user role ids").optional().custom((value, { req, location, path }) => {
        if (Array.isArray(value)) {
            const hasInValidId = value.some((val) => !validator.isMongoId(val));
            return !hasInValidId;
        } else {
            return false
        }
    }),
    query("tenantId", "Please provide valid tenant id").optional().isInt({ min: 1000 }).toInt(),
    ...projectionQueryValidator
];


exports.editCustomer = [
    header('devicetoken', 'deviceToken field is required').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("userRoleId", "Please provide user role id").trim().notEmpty().isMongoId(),
    body("firstName", "Please provide first name").trim().notEmpty(),
    body("lastName", "Please provide last name").trim().notEmpty(),
    body("countryCode", "Please provide country code").custom((value, { req, path, location }) => {
        return REGEX.COUNTRY_CODE.test(value);
    }).withMessage("Pleas provide valid country code"),
    body("mobileNumber", "Please provide mobile number").trim().notEmpty(),
    body("email", "Please provide email id").optional().trim().notEmpty().isEmail(),
    body("customerAppAccess", "Please provide customerAppAccess").optional().isBoolean().toBoolean(),
    body("isActive", "Please provide is active").isBoolean(),
    // body("shippingAddress", "Please provide shipping address").optional().trim().notEmpty(),
    body("longitude", "Please provide longitude").trim().notEmpty(),
    body("latitude", "Please provide latitude").trim().notEmpty(),
    body("shippingCountryId", "Please provide shipping country id").trim().notEmpty(),
    body("shippingRegionId", "Please provide shipping region id").optional().trim().notEmpty(),
    body("shippingCityId", "Please provide shipping city id").optional().trim().notEmpty(),
    // body("shippingCountryCode", "Please provide shipping country code").optional().trim().notEmpty(),
    // body("shippingMobileNumber", "Please provide shipping mobile number").optional().trim().notEmpty(),
    // body("externalId", "Please provide external id").optional().trim().notEmpty(),
    body("accountNumber", "Please provide account number").optional().trim(),
    body("customerName", "Please provide customer name").trim().notEmpty(),
    body("legalName", "Please provide legal name").trim().notEmpty(),
    body("salesPersonId", "Please provide sales person id").trim().notEmpty(),
    body("priceListId", "Please provide price list id").trim().notEmpty(),
    body("customerAppRequest", "Please provide valid customer app request").optional().isBoolean(),
    body("customerCatalogMode", "Please provide valid customer catalog modal").optional().isBoolean(),
];

exports.deleteCustomer = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    query("userRoleId", "Please provide user role id").trim().notEmpty().isMongoId(),
];

exports.updateCustomerAppAccess = [
    ...headerValidator,
    body("customerUserRoleIds", "Please provide valid customer user role ids").isArray()
        .custom((value) => Array.isArray(value) && value.every(v => validator.isMongoId(v))),
    body("accessInfo", "Please provide valid access info").isObject(),
    body("accessInfo.appAccess", "Please provide valid app access value").optional().toBoolean().isBoolean(),
    body("accessInfo.catalogMode", "Please provide valid catalog mode value").optional().toBoolean().isBoolean(),
]
exports.addCustomerDeviceAccess = [
    header('devicetoken', 'deviceToken field is required').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("userRoleId", "Please provide user role id").trim().notEmpty().isMongoId(),
    body("deviceId", "Please provide device id").trim().notEmpty(),
    body("deviceType", "Please provide device type").trim().notEmpty(),
    body("deviceOs", "Please provide device os").trim().notEmpty(),
];

exports.deleteCustomerDeviceAccess = [
    header('devicetoken', 'deviceToken field is required').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("userRoleId", "Please provide user role id").trim().notEmpty().isMongoId(),
    body("deviceId", "Please provide device id").trim().notEmpty(),
];

exports.tenantBranches = [
    header('devicetoken', 'deviceToken field is required').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    query("tenantId", "Please provide tenant id").isInt({ min: 1000 }).withMessage("Please provide tenant valid"),
    query("type").optional().isIn(Object.values(BRANCH_LIST_TYPE)),
];

exports.listTenantAndBranchUser = [
    ...tenantIdQueryValidator,
    ...searchKeyValidator,

    query("branchId")
        .optional()
        .isMongoId()
        .withMessage("Please provide valid branch id"),

    query("roleId")
        .optional()
        .isMongoId()
        .withMessage("Please provide valid role id"),

    query("status")
        .optional()
        .isIn(Object.values(ENTITY_STATUS))
        .withMessage(`value should be in ${Object.values(ENTITY_STATUS).join(", ")}`),

    query("type")
        .optional()
        .isIn(Object.values(LISTING_TYPES))
        .withMessage(`value should be in ${Object.values(LISTING_TYPES).join(", ")}`),

    query("sortBy")
        .default(TENANT_USER_SORT_BY.CREATED_AT)
        .isIn(Object.values(TENANT_USER_SORT_BY))
        .withMessage(`Please provide sortBy as in ${Object.values(TENANT_USER_SORT_BY).join(", ")}`),

    query("sortType")
        .default(SORT_TYPE.DESC)
        .isIn(Object.values(SORT_TYPE))
        .withMessage(`Please provide sortType as in ${Object.values(SORT_TYPE).join(", ")}`),
]

exports.changeTenantUserRoleStatus = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...statusBodyValidator,

    body("userRoleIds", "Please provide valid 'userRoleIds'")
        .isArray({ min: 1 }),
]

exports.getPriceList = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header("devicetoken", "deviceToken field is required!").trim().notEmpty(),
    query("tenantId", "Please provide tenant id").isInt({ min: 1000 }).withMessage("Please provide tenant valid")
];

exports.getCustomers = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...searchKeyValidator,

    query("branchId", "Please provide valid branch id").optional().isMongoId(),
    query("customerType", "Please provide valid customer type").isIn(Object.values(CUSTOMER_TYPE)),
    query("onlyCustomerCount", "Please provide boolean value of onlyCustomerCount parameter").optional().isBoolean().toBoolean()
];

exports.updateCustomersAccesses = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header("devicetoken", "deviceToken field is required!").trim().notEmpty(),
    body("statusType", "Please provide valid status type").trim().isIn(Object.values(CUSTOMER_STATUS_TYPE)),
    body("status", "Please provide status").isIn(Object.values(ENTITY_STATUS)).withMessage(`value should be in ${Object.values(ENTITY_STATUS).join(", ")}`),
    body("customers", "Please provide customers").isArray({ min: 1 })
];

exports.updateCustomers = [
    ...headerValidator,
    ...idsValidator,

    body("updateFields", "Please provide valid updateFields.")
        .custom(value => {
            return typeof value === "object" && Object.keys(value).length > 0
        }),

    body("tenantId", "Please provide valid tenant id")
        .if(
            body("updateFields.external_id")
                .trim()
                .notEmpty()
        )
        .toInt()
        .isInt({ min: 1000 })
]

exports.addUpdateCustomerShippingAddress = [
    ...headerValidator,
    body("shipping_address", "Please provide valid shipping address").trim().notEmpty(),
    body("shipping_country_id", "Please provide valid shipping country id").isMongoId(),
    body("shipping_city_id", "Please provide valid shipping city id").isMongoId(),
    body("shipping_region_id", "Please provide valid shipping region id").isMongoId(),
    body("gps_coordinates", "Please provide valid object of gps coordinate").isObject(),
    body("gps_coordinates.longitude", "Please provide valid object of gps coordinate").custom((value) => !isNaN(value)),
    body("gps_coordinates.latitude", "Please provide valid object of gps coordinate").custom((value) => !isNaN(value)),

    body("shipping_country_code", "Please provide valid shipping country code").custom((value) => REGEX.COUNTRY_CODE.test(value)),
    body("shipping_mobile_number", "Please provide valid shipping mobile number").trim().notEmpty(),
];

exports.searchCustomerByMobileNumber = [
    ...headerValidator,
    query("countryCode", "Please provide valid country code").custom((value) => REGEX.COUNTRY_CODE.test(value)),
    query("tenantId", "Please provide valid tenant id").isInt({ min: 1000 }).toInt(),
    query("mobileNumber", "Please provide valid mobile number").custom((value) => !isNaN(value))
]

exports.getConfigurations = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header("devicetoken", "deviceToken field is required!").trim().notEmpty(),
    query("tenantId", "Please provide tenant id").isInt({ min: 1000 }).withMessage("Please provide tenant valid"),
    query("type", "Please provide type").optional().trim().notEmpty()
];

exports.updateShippingLabelValidator = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...isActiveValidator,

    body("tenantLegalName", "Please provide valid tenant legal name.")
        .trim()
        .notEmpty(),

    body("branches", "Please provide at least one branch in array.")
        .isArray({ min: 1 }),

    body("branches.*.branchId", "Please provide valid branch id in branches array.")
        .isMongoId(),

    body("branches.*.name", "Please provide valid branch name in branches array.")
        .trim()
        .notEmpty(),

    body("branches.*.streetAddress", "Please provide valid street address in branches array.")
        .trim()
        .notEmpty(),

    body("branches.*.region", "Please provide valid region id in branches array.")
        .isMongoId(),

    body("branches.*.city", "Please provide valid city id in branches array.")
        .isMongoId(),

    body("branches.*.mobileNumber", "Please provide valid mobile number in branches array.")
        .trim()
        .notEmpty(),

    body("branches.*.mobileNumberCountryCode", "Please provide valid country code of mobile number in branches array.")
        .trim()
        .notEmpty(),

    body("branches.*.phoneNumber", "Please provide valid phone number in branches array.")
        .optional()
        .trim()
        .notEmpty(),

    body("branches.*.phoneNumberCountryCode", "Please provide valid country code of phone number in branches array.")
        .if(
            body("branches.*.phoneNumber")
                .trim()
                .notEmpty()
        )
        .trim()
        .notEmpty(),

    body("shippingLabelLogo", "Please provide shipping label logo")
        .trim()
        .notEmpty()
        .custom(value => {
            const ext = path.extname(value)

            if (!VALID_IMAGES_EXTS.includes(ext.toLocaleLowerCase())) {
                return false
            }
            return true
        })
        .withMessage(`Shipping label logo extension must be ${Object.values(VALID_IMAGES_EXTS).join(" or ")}`),
];

exports.updateAppSetting = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header("devicetoken", "deviceToken field is required!").trim().notEmpty(),
    body("tenantId", "Please provide tenant id").isInt({ min: 1000 }).withMessage("Please provide tenant valid"),
    body("quantityLabel", "Please provide quantity label").trim().notEmpty(),
    body("considerNewItem", "Please provide consider new item").trim().notEmpty(),
    body("priceChange", "Please provide price change").isBoolean(),
    body("hideOutOfStockProduct", "Please provide hide out of stock product").isBoolean(),
    body("reduceInventory", "Please provide reduce inventory").isBoolean(),
    body("customerAppAccess", "Please provide customer app access").isBoolean(),
    body("catalogMode", "Please provide catalog mode").isBoolean(),
    body("payment_voucher_whatsapp_notification", "Please provide payment voucher whatsapp notification").optional().isBoolean(),
    body("preferredLanguage", "Please provide preferred language").trim().notEmpty(),
    body("decimalPoint", "Please provide valid decimal point").optional().isInt({ min: 0 }).toInt(),

    body("customerAutoCatalogMode.enabled", "Please provide valid 'customerAutoCatalogMode.enabled'")
        .optional() //FIX:ME Remove when auto catalog changes pushed on production
        .isBoolean()
        .toBoolean(),

    body("customerAutoCatalogMode.duration", "Please provide valid 'customerAutoCatalogMode.duration'")
        .if((value, { req }) => {
            return req.body.customerAutoCatalogMode.enabled || (typeof value !== "undefined")
        })
        .notEmpty()
        .bail()
        .isInt({ min: 1 })
        .toInt()
        .withMessage("'customerAutoCatalogMode.duration' must be greater than 0")

    // body("prefix", "Please provide order prefix").trim(),
];


exports.allowNewValidation = [  // Dec 30 users and customer limit validation
    ...headerValidator,
    ...tenantIdValidator,
    query("type", "Please provide type").isIn(Object.values(TENANT_ALLOW_VALIDATION)).withMessage(`value should be in ${Object.values(TENANT_ALLOW_VALIDATION).join(", ")}`)
]; // Dec 30 users and customer limit validation


exports.getUserSettings = [
    ...headerValidator,
    ...tenantIdQueryValidator,
]

exports.updateUserSetting = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    body("masterPriceId", "Please provide master price id").trim().notEmpty().isMongoId(),

    body("out_of_stock", "Please provide valid 'out of stock' value.")
        .isObject(),

    body("out_of_stock.visible", "Please provide valid 'visible' vale in 'out of stock'")
        .isBoolean(),

    body("out_of_stock.searchable", "Please provide valid 'searchable' value in 'out of stock'")
        .isBoolean(),

    body("priceChange", "Please provide price change").isBoolean(),
    body("preferredLanguage", "Please provide language code").trim().notEmpty(),
]

exports.dataSheet = [
    ...headerValidator,
    body("tenantId", "Please provide tenant id").isInt({ min: 1000 }).withMessage("Please provide tenant valid"),
    body("dataType", "Please provide data type").isIn(Object.values(DATA_SHEET.DATA_TYPE)).withMessage(`dataType value should be in ${Object.values(DATA_SHEET.DATA_TYPE).join(", ")}`),
    body("operationType", "Please provide operation type").isIn(Object.values(DATA_SHEET.OPERATION_TYPE)).withMessage(`operationType value should be in ${Object.values(DATA_SHEET.OPERATION_TYPE).join(", ")}`)
]

exports.columnField = [
    ...headerValidator,
    query("dataType", "Please provide data type").isIn(Object.values([DATA_SHEET.DATA_TYPE.CUSTOMER, DATA_SHEET.DATA_TYPE.PRODUCT])).withMessage(`dataType value should be in ${Object.values([DATA_SHEET.DATA_TYPE.CUSTOMER, DATA_SHEET.DATA_TYPE.PRODUCT]).join(", ")}`),
]

exports.uploadSignatureValidator = [
    ...headerValidator,
    ...tenantIdValidator,
    body("dataType", "Please provide data type").isIn(Object.values(DATA_SHEET.DATA_TYPE)).withMessage(`dataType value should be in ${Object.values(DATA_SHEET.DATA_TYPE).join(", ")}`),
    body("operationType", "Please provide operation type").isIn(Object.values(DATA_SHEET.OPERATION_TYPE)).withMessage(`operationType value should be in ${Object.values(DATA_SHEET.OPERATION_TYPE).join(", ")}`)
]

exports.updateUploadFile = [
    ...headerValidator,
    ...tenantIdValidator,
    body("dataType", "Please provide data type").isIn(Object.values(DATA_SHEET.DATA_TYPE)).withMessage(`dataType value should be in ${Object.values(DATA_SHEET.DATA_TYPE).join(", ")}`),
    body("operationType", "Please provide operation type").isIn(Object.values(DATA_SHEET.OPERATION_TYPE)).withMessage(`operationType value should be in ${Object.values(DATA_SHEET.OPERATION_TYPE).join(", ")}`),
    body("fileName", "Please provide file name").trim().notEmpty()
]
exports.listDataSheet = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...searchKeyValidator,

    query("page", "Please provide page.").toInt().isInt({ min: 1 }).withMessage("Please provide valid page."),
    query("perPage", "Please provide per page limit.").toInt().isInt({ min: 1 }).withMessage("Please provide valid per page limit.")
]

exports.importValidation = [
    ...headerValidator,
    ...tenantIdValidator,
    body("fileId", "Please provide file id").trim().notEmpty().isMongoId(),
    body("approveType", "Please provide approveType").isIn(Object.values(DATA_SHEET.APPROVE_TYPE)).withMessage(`approveType value should be in ${Object.values(DATA_SHEET.APPROVE_TYPE).join(", ")}`),
    body("selectRow").optional().isArray()
]

exports.deleteDataSheetValidator = [
    ...headerValidator,
    ...tenantIdBodyValidator,

    body("fileIds", "Please provide file id array")
        .isArray()
]

exports.checkExistingExternalId = [
    ...headerValidator,
    ...tenantIdQueryValidator,

    query("accountNumber", "Please provide account number")
        .trim()
        .notEmpty(),
]

exports.tenantsForImageMatch = [
    ...headerValidator,
    ...tenantIdQueryValidator
]

exports.allowPayment = [
    ...headerValidator,

    body("status", "Please provide valid status")
        .isBoolean(),

    body("userRoleIds", "Please provide userRoleIds")
        .isArray({ min: 1 }),

    body("userRoleIds.*", "Please provide valid userRoleIds")
        .isMongoId()
]

const filtersQueryValidators = [
    query("salesPersonId", "Please provide valid salesPersonId")
        .optional()
        .isMongoId(),

    query("filters", "Please provide valid filters")
        .optional()
        .toArray()
        .isArray({ min: 1 }),
]

exports.getCustomersForPayment = [
    ...headerValidator,
    ...tenantIdQueryValidator,

    ...searchKeyValidator,
    ...filtersQueryValidators,

    query("type")
        .trim()
        .isIn(Object.values(CUSTOMER_PAYMENT_FILTER_TYPE))
        .withMessage(
            `Please provide type within ${Object.values(CUSTOMER_PAYMENT_FILTER_TYPE)}`
        ),

    query("perPage", "Please provide per page limit.")
        .if(
            query("type")
                .not()
                .equals(CUSTOMER_PAYMENT_FILTER_TYPE.WITHOUT_PAGINATION)
        )
        .toInt()
        .isInt({ min: 1 })
        .withMessage("Please provide valid per page limit."),

    query("page", "Please provide page.")
        .if(
            query("type")
                .not()
                .equals(CUSTOMER_PAYMENT_FILTER_TYPE.WITHOUT_PAGINATION)
        )
        .toInt()
        .isInt({ min: 1 })
        .withMessage("Please provide valid page."),
];

exports.getCustomersForRewardProgram = [
    ...headerValidator,
    ...tenantIdQueryValidator,

    ...perPageValidator,
    ...pageValidator,

    ...searchKeyValidator,
    ...filtersQueryValidators,

    query("type")
        .trim()
        .isIn(Object.values(CUSTOMER_REWARD_PROGRAM_FILTER_TYPE))
        .withMessage(
            `Please provide type within ${Object.values(CUSTOMER_REWARD_PROGRAM_FILTER_TYPE)}`
        ),
]

exports.listPaymentTerms = [
    ...headerValidator,
    ...tenantIdQueryValidator,
]

exports.scanQrCode = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...customerUserRoleIdBodyValidator,
    ...timezoneBodyValidator,
    body('qrInfo', "Please provide valid 'qrInfo'")
        .trim()
        .notEmpty(),
]

exports.listNotifications = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...perPageValidator,
    ...pageValidator,
    ...userRoleIdQueryValidator,
];
