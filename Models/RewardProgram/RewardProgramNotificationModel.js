const { NOTIFICATION, REWARD_PROGRAM } = require("../../Configs/constants")

const NotificationController = new (require("../../Controllers/NotificationController"))();
const TemplateService = new (require("../../Services/TemplateService"))();


module.exports = class {

    notificationTypes = {
        [REWARD_PROGRAM.POINT.LOG_TYPE.SAP.PAYMENT]: NOTIFICATION.TYPE.REWARD_PAYMENT,
        [REWARD_PROGRAM.POINT.LOG_TYPE.SAP.INVOICE.PURCHASE]: NOTIFICATION.TYPE.REWARD_PURCHASE,
        [REWARD_PROGRAM.POINT.LOG_TYPE.ACTION_TYPE.MEMBER_SCAN]: NOTIFICATION.TYPE.REWARD_MEMBER_SCAN,


        // [REWARD_PROGRAM.POINT.LOG_TYPE.ACTION_TYPE.MANUAL]: NOTIFICATION.TYPE.REWARD_PAYMENT,
    }

    sentRewardPointNotification = async (pointLog, session, milestoneNumber) => {
        if (pointLog.point_type !== REWARD_PROGRAM.POINT.TYPE.COINS) {
            return
        }

        let notificationType = this.notificationTypes[pointLog.log_type]
        if (!notificationType) {
            if (pointLog.log_type === REWARD_PROGRAM.POINT.LOG_TYPE.SYSTEM.MILESTONES) {
                const milestoneNotifications = {
                    1: NOTIFICATION.TYPE.REWARD_MILESTONE_ONE,
                    2: NOTIFICATION.TYPE.REWARD_MILESTONE_TWO,
                    3: NOTIFICATION.TYPE.REWARD_MILESTONE_THREE,
                }
                notificationType = milestoneNotifications[milestoneNumber]

                if (!notificationType) {
                    return
                }
            }
            else {
                return
            }
        }

        const notificationData = {
            ...TemplateService.getRewardCoinsNotificationTemplate(
                notificationType,
                pointLog.points,
                pointLog.point_type
            ),
            userRoleIds: [pointLog.customer_user_role_id],
            tenantId: pointLog.tenant_id,
            type: notificationType,
            threadId: notificationType,
            payloadData: {
                tenantId: pointLog.tenant_id,
                points: pointLog.points,
                logType: pointLog.log_type,
                pointType: pointLog.point_type,
                entryType: pointLog.entry_type
            },
        }

        await NotificationController.sentNotification(notificationData, session)
    }

}
