const UserAuthModal = new (require("./auth"))()

const {
    countries: CountrySchema,
    users: UserSchema,
    regions: RegionSchema,
    cities: CitySchema,
    languages: LanguageSchema,
    user_roles: UserRoleSchema,
} = require("../Database/Schemas");

const {
    LISTING_TYPES,
    ENTITY_STATUS,
    PRIMITIVE_ROLES,
} = require("../Configs/constants");

class CommonModal {
    async addCountry(body, headers) {
        return CountrySchema({
            name: body.countryName,
            country_code: body.countryCode,
            timezone: body.timeZone,
            mobile_number_format: body.mobileNumberFormat,
            alpha_two_code: body.alphaTwoCode,
            currency: body.currency,
            is_active: body.isActive,
            is_deleted: false,
            created_by: headers.userDetails._id,
            updated_by: headers.userDetails._id,
            vat: body?.vat || 0,
            secondary_language_code: body.secondaryLanguage,
            secondary_language_name: body.secondaryLanguageName,
            google_place_id: body.googlePlaceId
        });
    };

    async getActiveCountries(body, headers) {
        const { type = "", searchKey = "" } = body;

        switch (type) {
            case LISTING_TYPES.ALL:
                return CountrySchema.find({ is_deleted: false });

            case LISTING_TYPES.SEARCH:
                var query = {
                    is_deleted: false,
                }

                if (searchKey) {
                    query.name = new RegExp(searchKey, "i")
                }
                return CountrySchema.find(query);

            case LISTING_TYPES.PAGINATION:
                var query = {
                    is_deleted: false,
                }

                if (searchKey) {
                    query.$or = [
                        { name: new RegExp(searchKey, "i") },
                        { country_code: new RegExp(searchKey) },
                        // {currency: RegExp(searchKey, "i")}
                    ]
                }

                var { page = 1, perPage = 10 } = body;
                page = parseInt(page);
                perPage = parseInt(perPage);
                const offset = perPage * (page - 1);

                const [list, count] = await Promise.all([
                    CountrySchema.find(query, null, { skip: offset, limit: perPage }),
                    CountrySchema.countDocuments(query)
                ])

                return { list, count };

            default:
                return CountrySchema.find({ is_deleted: false });
        }
    }

    async findCountryFromCode(body, headers) {
        return CountrySchema.findOne({ country_code: body.countryCode });
    }

    async findCountryById(country_id, projection = {}) {
        return CountrySchema.findById(country_id, projection);
    }

    async findCountries(filter = {}, projection = "", options = {}) {
        return CountrySchema.find(filter, projection, options)
    }

    async restoreCountryById(country_id) {
        return CountrySchema.updateOne({ _id: country_id }, { is_deleted: false })
    }

    async findCountryCountWithName(body, headers) {
        const pipeline = [
            {
                $addFields: {
                    lowerName: {
                        $toLower: "$name"
                    },
                    // lowerAlphaCode: {
                    //     $toLower: "$alpha_two_code"
                    // }
                }
            },
            {
                $match: { lowerName: body.countryName.toLowerCase() }
            },
            // {
            //     $match: {
            //         $or: [
            //             { lowerName: body.countryName.toLowerCase() },
            //             // { lowerAlphaCode: body.alphaTwoCode.toLowerCase() }
            //         ]
            //     }
            // },
        ]

        if (!body.expectCountryId) {
            return CountrySchema.aggregate(pipeline);
        } else {
            pipeline.unshift({
                $match: {
                    _id: { $ne: new mongoose.Types.ObjectId(body.expectCountryId) }
                }
            });
            return CountrySchema.aggregate(pipeline);
        }
    }

    async deleteCityWithCountryId(country_id) {
        return CitySchema.updateMany({ country_id }, { is_deleted: true })
    }

    async deleteRegionWithCountryId(country_id) {
        return RegionSchema.updateMany({ country_id }, { is_deleted: true })
    }

    async deleteCountryById(country_id) {
        return CountrySchema.updateOne({ _id: country_id }, { is_deleted: true });
    }

    async deleteCitiesWithRegionId(region_id) {
        return CitySchema.updateMany({ region_id }, { is_deleted: true });
    }

    async deleteRegionById(region_id) {
        return RegionSchema.updateOne({ _id: region_id }, { is_deleted: true });
    }

    async findUsersFromEmail(email) {
        if (email.includes(".")) {
            email.replace(".", "[.]")
        }
        if (email.includes("+")) {
            email.replace("+", "[+]");
        }

        return UserSchema.find({ email: { $regex: new RegExp(`.*${email}.*`, 'i') }, is_active: true, is_deleted: false });
    }

    async findUsersFromMobile(countryCode, mobileNumber) {
        if (mobileNumber.includes(".")) {
            mobileNumber.replace(".", "[.]")
        }
        if (mobileNumber.includes("+")) {
            mobileNumber.replace("+", "[+]");
        }
        const pipeline = [
            {
                $match: {
                    country_code: countryCode,
                    is_active: true,
                    is_deleted: false
                },
            },
            {
                $addFields: {
                    strMobileNumber: {
                        $toString: { $toLong: "$mobile_number" }
                    }
                }
            },
            {
                $match: {
                    strMobileNumber: { $regex: new RegExp(`.*${mobileNumber}.*`) }
                }
            }
        ]
        return UserSchema.aggregate(pipeline);

        // return UserSchema.find({mobile_number: { $regex: new RegExp(`.*${mobileNumber}.*`, 'i')}, country_code: countryCode, is_active: true, is_deleted: false});
    }

    async findExistingRegionWithName(body, headers) {
        const searchKey = body.name.toLowerCase()

        const pipeline = [
            {
                $match: {
                    country_id: new mongoose.Types.ObjectId(body.countryId),
                }
            },
            {
                $addFields: {
                    lowerName: {
                        $toLower: "$name"
                    },
                    lowerSecondaryName: {
                        $toLower: "$secondary_language_name"
                    }
                }
            },
            {
                $match: {
                    $or: [
                        {
                            lowerName: searchKey,
                        },
                        {
                            lowerSecondaryName: searchKey,
                        }
                    ]
                }
            },
        ];

        if (!body.expectRegionId) {
            return RegionSchema.aggregate(pipeline);
        } else {
            pipeline.unshift({
                $match: {
                    _id: { $ne: new mongoose.Types.ObjectId(body.expectRegionId) }
                }
            });
            return RegionSchema.aggregate(pipeline);
        }
    }

    async findExistingRegionWithCode(body, headers) {
        const searchKey = body.code.toLowerCase()

        const pipeline = [
            {
                $match: {
                    country_id: new mongoose.Types.ObjectId(body.countryId),
                }
            },
            {
                $addFields: {
                    lowerCode: {
                        $toLower: "$code"
                    },
                    lowerSecondaryCode: {
                        $toLower: "$secondary_language_code"
                    }
                }
            },
            {
                $match: {
                    $or: [
                        {
                            lowerCode: searchKey,
                        },
                        {
                            lowerSecondaryCode: searchKey,
                        }
                    ]
                }
            },
        ];

        if (!body.expectRegionId) {
            return RegionSchema.aggregate(pipeline);
        } else {
            pipeline.unshift({
                $match: {
                    _id: { $ne: new mongoose.Types.ObjectId(body.expectRegionId) }
                }
            });
            return RegionSchema.aggregate(pipeline);
        }
    }

    async updateCountryOfCities(region_id, country_id) {
        return CitySchema.updateMany({ region_id }, { country_id })
    }

    async restoreRegionById(region_id) {
        return RegionSchema.updateOne({ _id: region_id }, { is_deleted: false });
    }

    async addRegion(body, headers) {
        return RegionSchema({
            country_id: body.countryId,
            is_active: body.isActive,
            name: body.name,
            is_deleted: false,
            code: body.code,
            secondary_language_code: body.secondaryLanguage,
            secondary_language_name: body.secondaryLanguageName,
            created_by: headers.userDetails._id,
            updated_by: headers.userDetails._id,
        })
    }

    async findExistingCity(body, headers) {
        const searchKey = body.name.toLowerCase()

        const pipeline = [
            {
                $match: {
                    country_id: new mongoose.Types.ObjectId(body.countryId),
                    region_id: new mongoose.Types.ObjectId(body.regionId)
                }
            },
            {
                $addFields: {
                    lowerName: {
                        $toLower: "$name"
                    },
                    lowerSecondaryName: {
                        $toLower: "$secondary_language_name"
                    }
                }
            },
            {
                $match: {
                    $or: [
                        {
                            lowerName: searchKey,
                        },
                        {
                            lowerSecondaryName: searchKey,
                        }
                    ]
                }
            },
        ];

        if (!body.expectCityId) {
            return CitySchema.aggregate(pipeline);
        } else {
            pipeline.unshift({
                $match: {
                    _id: { $ne: new mongoose.Types.ObjectId(body.expectCityId) }
                }
            });
            return CitySchema.aggregate(pipeline);
        }
    }

    async addCity(body, headers) {
        return CitySchema({
            name: body.name,
            region_id: body.regionId,
            country_id: body.countryId,
            is_deleted: false,
            is_active: body.isActive,
            created_by: headers.userDetails._id,
            updated_by: headers.userDetails._id,
            secondary_language_name: body.secondaryLanguageName
        });
    }

    async restoreCityById(city_id) {
        return CitySchema.updateOne({ _id: city_id }, { is_deleted: false, is_active: true });
    }

    async getRegions(body, headers) {
        const { countryId = "", searchKey = "", type = "", status = "" } = body;
        const pipeline = []
        switch (type) {
            case LISTING_TYPES.ALL: // all non deleted regions
                return RegionSchema.find({ is_deleted: false }).populate({ path: 'country_id', select: 'name id' });

            case LISTING_TYPES.SEARCH: // can support filter of country and search with search key (only search with in region name)
                const $match = {
                    "is_deleted": false,
                }

                if (countryId) {
                    $match["country_id"] = new mongoose.Types.ObjectId(countryId)
                }

                if (searchKey) {
                    $match["name"] = new RegExp(searchKey, "i")
                }

                pipeline.push({
                    $match,
                });
                return RegionSchema.aggregate(pipeline);

            case LISTING_TYPES.PAGINATION: // pagination with filter of country and search key (which will also search country if country id is not given)
                let { page = 1, perPage = 10 } = body;
                const offset = perPage * (page - 1);
                page = parseInt(page);
                perPage = parseInt(perPage);

                if (countryId) {
                    const query = {
                        is_deleted: false,
                        country_id: new mongoose.Types.ObjectId(countryId),
                    };
                    if (status) {
                        query['is_active'] = status === ENTITY_STATUS.ACTIVE ? true : false
                    }

                    if (searchKey) {
                        query["name"] = new RegExp(searchKey, "i")
                    }

                    pipeline.push(
                        {
                            $match: query
                        },
                        {
                            $lookup: {
                                from: "countries",
                                localField: "country_id",
                                foreignField: "_id",
                                as: "country_id"
                            },
                        },
                        {
                            $addFields: {
                                country_id: { $first: "$country_id" },
                            },
                        },
                    );
                } else {
                    const query = {
                        is_deleted: false,
                    };
                    if (status) {
                        query['is_active'] = status === ENTITY_STATUS.ACTIVE ? true : false
                    }
                    pipeline.push(
                        {
                            $match: query
                        },
                        {
                            $lookup: {
                                from: "countries",
                                localField: "country_id",
                                foreignField: "_id",
                                as: "country_id"
                            },
                        },
                        {
                            $addFields: {
                                country_id: { $first: "$country_id" },
                            },
                        },
                    );

                    if (searchKey) {
                        pipeline.push({
                            $match: {
                                $or: [
                                    { name: new RegExp(searchKey, "i") },
                                    { "country_id.name": new RegExp(searchKey, "i") }
                                ]
                            }
                        })
                    }
                }
                pipeline.push(
                    {
                        $project: {
                            _id: 1,
                            code: 1,
                            name: 1,
                            is_active: 1,
                            secondary_language_name: 1,
                            country_id: {
                                name: 1,
                                _id: 1,
                            }
                        }
                    },
                    {
                        $facet: {
                            list: [{ $skip: offset }, { $limit: perPage }],
                            count: [{ $count: "count" }]
                        }
                    },
                    {
                        $unwind: "$count"
                    }
                )
                const data = await RegionSchema.aggregate(pipeline);
                const list = data.length ? data[0].list : [];
                const count = data.length ? data[0].count.count : 0;
                return { list, count };
            default:
                if (countryId) {
                    return RegionSchema.find({ is_deleted: false, country_id: countryId }).populate({ path: 'country_id', select: 'name id' });
                } else {
                    return RegionSchema.find({ is_deleted: false }).populate({ path: 'country_id', select: 'name id' });
                }
                break
        }
    }

    async getRegionById(region_id, projection = {}) {
        return RegionSchema.findById(region_id, projection);
    }

    async getCities(body, headers) {
        const {
            countryId = "",
            regionId = "",
            searchKey = "",
            type = "",
            status = "",
            cityIds,
            projections = []
        } = body;

        const pipeline = [];
        switch (type) {
            case LISTING_TYPES.ALL:
                return CitySchema.find({ is_deleted: false });

            case LISTING_TYPES.SEARCH: {
                const match = {
                    is_deleted: false
                }

                if (searchKey) {
                    match["name"] = new RegExp(searchKey, "i")
                }

                return CitySchema
                    .find(match)
                    .populate({ path: "country_id", select: 'name _id' })
                    .populate({ path: "region_id", select: 'name _id' });
            }

            case LISTING_TYPES.PAGINATION:
                let { page = 1, perPage = 10 } = body;
                const offset = perPage * (page - 1);
                page = parseInt(page);
                perPage = parseInt(perPage);
                pipeline.push(
                    {
                        $lookup: {
                            from: "countries",
                            localField: "country_id",
                            foreignField: "_id",
                            as: "country_id"
                        },
                    },
                    {
                        $lookup: {
                            from: "regions",
                            localField: "region_id",
                            foreignField: "_id",
                            as: "region_id"
                        },
                    },
                    {
                        $addFields: {
                            country_id: { $first: "$country_id" },
                            region_id: { $first: "$region_id" }
                        },
                    }
                )
                if (countryId && !regionId) {
                    // apply filter of country first
                    const query = {
                        is_deleted: false,
                        country_id: new mongoose.Types.ObjectId(countryId),
                    };
                    if (status) {
                        query['is_active'] = status === ENTITY_STATUS.ACTIVE ? true : false
                    }

                    if (searchKey) {
                        query["$or"] = [
                            // need to search with region name too.
                            { "region_id.name": new RegExp(searchKey, "i") },
                            { name: new RegExp(searchKey, "i") }
                        ]
                    }

                    pipeline.unshift({
                        $match: query
                    })

                } else if (!countryId && regionId) {
                    // apply filter of region first
                    const query = {
                        is_deleted: false,
                        region_id: new mongoose.Types.ObjectId(regionId),
                    };
                    if (status) {
                        query['is_active'] = status === ENTITY_STATUS.ACTIVE ? true : false
                    }

                    if (searchKey) {
                        query["$or"] = [
                            // need to search with country name too.
                            { "country_id.name": new RegExp(searchKey, "i") },
                            { name: new RegExp(searchKey, "i") }
                        ]
                    }

                    pipeline.unshift({
                        $match: query
                    })
                }
                else if (countryId && regionId) {
                    // apply filter of country and region first
                    const query = {
                        is_deleted: false,
                        region_id: new mongoose.Types.ObjectId(regionId),
                        country_id: new mongoose.Types.ObjectId(countryId),
                    };

                    if (searchKey) {
                        query["name"] = new RegExp(searchKey, "i") // now only search with in city names
                    }

                    if (status) {
                        query['is_active'] = status === ENTITY_STATUS.ACTIVE ? true : false
                    }
                    pipeline.unshift(
                        {
                            $match: query
                        },
                    )
                } else {
                    const query = {
                        is_deleted: false,
                    };
                    if (status) {
                        query['is_active'] = status === ENTITY_STATUS.ACTIVE ? true : false
                    }
                    // if region and country both not provided 

                    if (searchKey) {
                        query["$or"] = [
                            // need to search with country and region name too.
                            { "country_id.name": new RegExp(searchKey, "i") },
                            { "region_id.name": new RegExp(searchKey, "i") },
                            { name: new RegExp(searchKey, "i") }
                        ]
                    }

                    pipeline.unshift({
                        $match: query
                    })
                }

                pipeline.push(
                    {
                        $project: {
                            _id: 1,
                            code: 1,
                            name: 1,
                            is_active: 1,
                            secondary_language_name: 1,
                            country_id: {
                                name: 1,
                                _id: 1,
                            },
                            region_id: {
                                name: 1,
                                _id: 1,
                            }
                        }
                    },
                    {
                        $facet: {
                            list: [{ $skip: offset }, { $limit: perPage }],
                            count: [{ $count: "count" }]
                        }
                    },
                    {
                        $unwind: "$count"
                    }
                );
                const data = await CitySchema.aggregate(pipeline)
                const list = data.length ? data[0].list : [];
                const count = data.length ? data[0].count.count : 0;
                return { list, count };

            default:
                const match = {
                    is_deleted: false
                }

                if (countryId) {
                    match.country_id = countryId
                }

                if (regionId) {
                    match.region_id = regionId
                }

                if (cityIds) {
                    match._id = {
                        $in: cityIds
                    }
                }

                return CitySchema.find(
                    match,
                    projections.join(" "),
                    {
                        lean: true
                    }
                );
        }
    }

    async getCityById(city_id) {
        return CitySchema.findById(city_id);
    }

    async changeCitiesStatus(body, headers) {
        return CitySchema.updateMany({ _id: { $in: body.cities } }, { is_active: body.status === ENTITY_STATUS.ACTIVE, updated_by: headers.userDetails._id });
    }

    async deleteCities(body, headers) {
        return CitySchema.updateMany({ _id: { $in: body.cities } }, { is_deleted: true, updated_by: headers.userDetails._id });
    }

    async regionListByCountry(countryId) {
        return RegionSchema.find({ country_id: countryId, is_deleted: false, is_active: true })
    }

    async cityListByRegion(countryId, regionId) {
        return CitySchema.find({ country_id: countryId, region_id: regionId, is_deleted: false, is_active: true })
    }

    async findCountryByName(name) {
        return CountrySchema.findOne({ name: name, is_deleted: false, is_active: true });
    }

    async findRegionByName(name) {
        return RegionSchema.findOne({ name: name, is_deleted: false, is_active: true });
    }

    async findCityByName(name) {
        return CitySchema.findOne({ name: name, is_deleted: false, is_active: true });
    }

    async getLanguageByCode(code = "en") {
        return LanguageSchema.findOne({ language_code: code });
    }

    async getLanguages(body, headers) {
        const { type = "" } = body;
        switch (type) {

            case LISTING_TYPES.ALL:
                return LanguageSchema.find({});

            case LISTING_TYPES.PAGINATION:
                let { page = 1, perPage = 10 } = body;
                page = parseInt(page);
                perPage = parseInt(perPage);
                const offset = perPage * (page - 1)
                var query = [
                    {
                        '$lookup': {
                            'from': 'countries',
                            'localField': 'language_code',
                            'foreignField': 'secondary_language_code',
                            'as': 'countries'
                        }
                    }, {
                        '$project': {
                            '_id': 1,
                            'name': 1,
                            'language_code': 1,
                            'countries.name': 1,
                            'key_count': 1,
                            'enable_rtl': 1,
                        }
                    }, {
                        $facet: {
                            list: [{ $skip: offset }, { $limit: perPage }],
                            count: [{ $count: "count" }]
                        }
                    },
                    {
                        $unwind: "$count"
                    }
                ]
                const dataList = await LanguageSchema.aggregate(query)
                const list = dataList[0]?.list.length > 0 ? dataList[0].list : []
                const count = dataList[0]?.count ? dataList[0].count : 0;
                return { list, count };

            default:
                return LanguageSchema.find({});
        }
    }

    async getProfilesForNotifications(
        filterItems = {
            tenant_id: "",
            branchId: "",
            tenantRoles: [],
            branchRoles: [],
            notificationType: "",
            allowedNotificationFactor: "", // for e.g. allow_email_notification, allow_sms_notification, allow_push_notification
        },
        projection = {},
        options = {},
        populationArray = []
    ) {
        const { tenant_id, branchId, tenantRoles, branchRoles, notificationType, allowedNotificationFactor } = filterItems;

        const filter = {
            tenant_id: tenant_id,
            is_deleted: false,
            is_active: true,
            notifications: { $elemMatch: { notification_type: notificationType, [allowedNotificationFactor]: true } },
            // "notifications.allow_email_notification": true,
            $or: [{ role_id: { $in: tenantRoles } }]
        }

        // filter[`notifications.${allowedNotificationFactor}`] = true

        if (branchId && branchRoles?.length) {
            filter["$or"].push({ brach_id: branchId, role_id: { $in: branchRoles } })
        }

        return UserAuthModal.getUserRolesWithPopulation(filter, projection, options, populationArray);
    }

    async generateDynamicLink(body) {
        const orderId = body._id;
        const roleType = body.roleType;
        const token = body.token;
        let customer = false;
        if (roleType === PRIMITIVE_ROLES.CUSTOMER) {
            customer = true
        }
        let androidInfo = {
            androidPackageName: process.env.PACKAGE_NAME,
        }

        let iosInfo = {
            iosBundleId: process.env.PACKAGE_NAME,
            iosAppStoreId: process.env.APPLE_STORE_ID
        }
        let link;
        if (!customer) {
            link = `${process.env.REACT_APP_URL}order-link/${orderId}?token=${token}`;
            androidInfo["androidFallbackLink"] = `${process.env.REACT_APP_URL}order-link/${orderId}?token=${token}`;
            iosInfo["iosFallbackLink"] = `${process.env.REACT_APP_URL}order-link/${orderId}?token=${token}`
        } else {
            link = `${process.env.APPSTORE_URL}?orderid=${orderId}&token=${token}`;
        }
        const response = await this.generateFireBaseDL(link, androidInfo, iosInfo);

        const data = await response.json();

        if (!response.ok) {
            logger.info(`common.js:837 ~ CommonModal ~ generateDynamicLink ~ data: ${data}`)
        }
        return data?.shortLink;
    }

    generateProcessOrderDynamicLink(body) {
        const orderId = body._id;
        const token = body.token;

        return `${process.env.REACT_APP_URL}orders/${orderId}?token=${token}`;
    }

    async generateFireBaseDL(link, androidInfo, iosInfo) {
        return fetch(process.env.DYNAMIC_LINK_URL + process.env.FIREBASECONFIG_API_KEY, {
            method: "POST",
            body: JSON.stringify({
                dynamicLinkInfo: {
                    domainUriPrefix: process.env.DOMAIN_URI,
                    link: link,
                    androidInfo: androidInfo,
                    iosInfo: iosInfo
                },
                suffix: {
                    option: "SHORT",
                },
            }),
        });
    }

    async getCommonProfilesForOrderEmail(tenantId, tenantRoles, branchId, branchRole) {
        const emailProfilePopulateArray = [
            {
                path: "user_id",
                select: { email: 1, country_code: 1, mobile_number: 1 }
            }
        ];

        return UserAuthModal.getUserRolesWithPopulation(
            {
                tenant_id: tenantId,
                is_deleted: false,
                is_active: true,
                $or: [
                    { role_id: { $in: tenantRoles.map(r => r._id), } },
                    { brach_id: branchId, role_id: branchRole._id }
                ],
            },
            { notifications: 1, brach_id: 1, tenant_id: 1, user_id: 1, _id: 1, role_id: 1, collection_name: 1 },
            { lean: true, populate: emailProfilePopulateArray }
        );
    }

    async addUpdateImage(body, header) {
        const { imageName } = body;

        const setFields = {
            profile_pic: imageName,
            updated_by: header.userDetails._id,
        };

        const userRoleId = imageName.split('.')[0];

        return await UserRoleSchema.findOneAndUpdate(
            {
                "_id": userRoleId,
            },
            {
                "$set": setFields
            },
            {
                new: true
            }
        );
    }

    transactionCallback = async (callback) => {
        const session = await mongoose.startSession()

        try {
            await session.withTransaction(async () => {
                await callback(session)
            })
        }
        catch (error) {
            throw error
        }
        finally {
            session.endSession();
        }
    }

}

module.exports = CommonModal;
