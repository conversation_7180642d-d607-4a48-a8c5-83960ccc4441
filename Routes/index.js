module.exports = app => {
    app.get('/', (req, res) => {
        res.status(STATUS_CODES.SUCCESS)
            .send("Welcome to " + process.env.PROJECT_NAME + " " + process.env.DB_NAME)
    });

    app.use("/auth", require("./auth"));
    app.use("/common", require("./common"));
    app.use("/role", require("./roles"));
    app.use("/system-portal", require("./systemPortal"));
    app.use("/tenant-portal", require("./tenantPortal"));
    app.use("/language", require("./language"));
    app.use("/internalService", require("./InternalServiceRoutes"));
    app.use("/integrationCredential", require("./IntegrationCredentialRoutes"));
    app.use("/holdReason", require("./HoldReasonRoutes"));
    app.use("/holdReasonTemplateOptions", require("./HoldReasonTemplateOptionsRoutes"));
}
