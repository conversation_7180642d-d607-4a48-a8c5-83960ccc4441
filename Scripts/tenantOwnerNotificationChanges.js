var cursor = db.tenant_notification_configurations.find({});

cursor.forEach(n => {
   var tenant_id = n.tenant_id;
   var notifications = n.notifications;

   db.user_roles.updateOne({ tenant_id: tenant_id },  { $set: { notifications: notifications } })
});

// add warehouses to existing branches
var cursor = db.tenant_branches.find({});

cursor.forEach(br => {
   var tenant_id = br.tenant_id;
   var branch_id = br._id;

   db.tenant_branches.updateOne({ _id: branch_id }, { $set: { warehouse_counter: 1 } });
   var user = ObjectId("62e275ac0b77a6bbdf16fac8");
   db.warehouses.insertOne({ created_by: user, updated_by: user, created_at: ISODate(), updated_at: ISODate(), brach_id: branch_id, tenant_id: tenant_id, warehouse_code: "WH01", is_active: true, is_deleted: false });
});


var cursor = db.user_roles.find({});

cursor.forEach(doc => {
   db.user_roles.updateOne({ _id: doc._id }, { $set: { collection_name: "users" } })
})


var cursor = db.products.find({});

cursor.forEach(pro => {
   var item_numbers = [];
   if(pro.type == "VARIANT") {
      var variant = db.product_variants.find({tenant_id: pro.tenant_id, product_id: pro._id}).toArray();
      variant.forEach(vr => {
         item_numbers.push(vr.item_number);
      });
   }
   db.products.updateOne({ _id: pro._id }, { $set: { variant_item_numbers: item_numbers  } })
});

var cursor = db.user_roles.find({role_id: ObjectId("634fbeaf6d1d2d861f6e6a1f")});

cursor.forEach(cus => {
   db.user_roles.updateOne({ _id: cus._id }, { $unset: { "tablet_shipping_address.gps_coordinates._id": "", "portal_shipping_address.gps_coordinates._id": "" } });
});

cursor.forEach(cus => {
   var shipping_address = {
      shipping_address: cus.shipping_address,
      shipping_country_id: cus.shipping_country_id,
      shipping_city_id: cus.shipping_city_id,
      shipping_region_id: cus.shipping_region_id,
      shipping_country_code: cus.shipping_country_code,
      shipping_mobile_number: cus.shipping_mobile_number,
      gps_coordinates: cus.gps_coordinates,
   };

   var unset = {
      shipping_address: "",
      shipping_country_id: "",
      shipping_city_id: "",
      shipping_region_id: "",
      shipping_country_code: "",
      shipping_mobile_number: "",
      gps_coordinates: "",
   }
   db.user_roles.updateOne({ _id: cus._id }, { $set: { portal_shipping_address: shipping_address, tablet_shipping_address: shipping_address}, $unset: unset });
});

var cursor = db.images.find({});

cursor.forEach(img => {
   var imageName = img.image_name;

   var start = imageName.lastIndexOf("_P") + 2;
   var end = imageName.lastIndexOf(".");
   db.images.updateOne({ _id: img._id }, { $set: { image_number: parseInt(imageName.substring(start, end)) } })
})


// ============  item_comment changes   ============  //

var cursor = db.order_cart_items.find({});

cursor.forEach(doc => {
   var comt = doc.comment ? doc.comment : [];
   db.order_cart_items.updateOne({ _id: doc._id }, { $set: { item_comment: comt } });
});

var cursor = db.tenant_order_items.find({});

cursor.forEach(doc => {
   var comt = doc.comment ? doc.comment : [];
   db.tenant_order_items.updateOne({ _id: doc._id }, { $set: { item_comment: comt } });
});