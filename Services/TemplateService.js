const {
    NOTIFICATION,
    PRIMITIVE_ROLES,
} = require("../Configs/constants")

module.exports = class {

    getOrderNotificationTemplate = (
        notificationType,
        userRole,
        orderInfo
    ) => {
        const {
            orderId,
            salesPersonName,
            customerLegalName,
            amount,
            currency
        } = orderInfo

        const smsMessages = {
            [NOTIFICATION.TYPE.ORDER_FOR_APPROVAL]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `Your order ${orderId} has been successfully sent for approval. Thank you for your patience.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `New order ${orderId} for ${customerLegalName} has been successfully sent for approval.`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `New order ${orderId} for ${customerLegalName} has been successfully sent for approval.`,
            },

            [NOTIFICATION.TYPE.ORDER_APPROVED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `Your order ${orderId} has been approved. Thank you for your patience.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `New order ${orderId} for ${customerLegalName} has been approved.`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `New order ${orderId} for ${customerLegalName} has been approved.`,
            },

            [NOTIFICATION.TYPE.ORDER_RECEIVED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `Great news! We’ve received your order ${orderId} and will process it soon. Thank you for your business.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `New order ${orderId} has been placed successfully.`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `New order ${orderId}
Salesperson: ${salesPersonName}
Customer: ${customerLegalName}
Value: ${amount} ${currency}`,
            },

            [NOTIFICATION.TYPE.ORDER_PREPARING]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `Your order ${orderId} is under processing.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `Order ${orderId} for ${customerLegalName} is under processing.`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `Order ${orderId} is under processing.
Salesperson: ${salesPersonName}
Customer: ${customerLegalName}
Value: ${amount} ${currency}`,
            },

            [NOTIFICATION.TYPE.ORDER_SHIPPED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `Your order ${orderId} has been shipped.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `Order ${orderId} for ${customerLegalName} has been shipped.`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `Order ${orderId} has been shipped.
Salesperson: ${salesPersonName}
Customer: ${customerLegalName}
Value: ${amount} ${currency}`,
            },

            [NOTIFICATION.TYPE.ORDER_DELIVERED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `Your order ${orderId} has been delivered.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `Order ${orderId} for ${customerLegalName} has been delivered.`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `Order ${orderId} has been delivered
Salesperson: ${salesPersonName}
Customer: ${customerLegalName}
Value: ${amount} ${currency}`,
            },
        }
        const notificationTitle = {
            [NOTIFICATION.TYPE.ORDER_FOR_APPROVAL]: "Pending Approval",
            [NOTIFICATION.TYPE.ORDER_APPROVED]: "Order Approved",
            [NOTIFICATION.TYPE.ORDER_RECEIVED]: "Order Received",
            [NOTIFICATION.TYPE.ORDER_PREPARING]: "Preparing Order",
            [NOTIFICATION.TYPE.ORDER_SHIPPED]: "Order Shipped",
            [NOTIFICATION.TYPE.ORDER_DELIVERED]: "Order Delivered",
        }
        const notificationSecondaryLanguageTitle = {
            [NOTIFICATION.TYPE.ORDER_FOR_APPROVAL]: "بانتظار الموافقة",
            [NOTIFICATION.TYPE.ORDER_APPROVED]: "تمت الموافقة على الطلب",
            [NOTIFICATION.TYPE.ORDER_RECEIVED]: "تم استلام الطلب",
            [NOTIFICATION.TYPE.ORDER_PREPARING]: "جاري التحضير",
            [NOTIFICATION.TYPE.ORDER_SHIPPED]: "تم الشحن",
            [NOTIFICATION.TYPE.ORDER_DELIVERED]: "تم التوصيل",
        }
        const notificationMessage = {
            [NOTIFICATION.TYPE.ORDER_FOR_APPROVAL]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `📤 Your order #${orderId} has been sent for approval.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `📤 ${customerLegalName} has sent an order #${orderId} for approval.`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `📤 ${customerLegalName} has sent an order #${orderId} for approval.`,
            },

            [NOTIFICATION.TYPE.ORDER_APPROVED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `✅ Great news! Order #${orderId} is approved`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `✅ Order from ${customerLegalName} was approved. Order #${orderId}`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `✅ Order from ${customerLegalName} was approved. Order #${orderId}`,
            },

            [NOTIFICATION.TYPE.ORDER_RECEIVED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `📦 We've received your order #${orderId}. Thanks!`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `🆕 New order for ${customerLegalName} has been placed successfully.`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `🆕 New order for ${customerLegalName} has been placed successfully.`,
            },

            [NOTIFICATION.TYPE.ORDER_PREPARING]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `🔧 We're preparing your order #${orderId}.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `🔧 Order for ${customerLegalName} is being prepared. Order #${orderId}`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `🔧 Order for ${customerLegalName} is being prepared. Order #${orderId}`,
            },

            [NOTIFICATION.TYPE.ORDER_SHIPPED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `🚚 Your order #${orderId} is on its way!`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `🚚 Order for ${customerLegalName} has shipped. Order #${orderId}`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `🚚 Order for ${customerLegalName} has shipped. Order #${orderId}`,
            },

            [NOTIFICATION.TYPE.ORDER_DELIVERED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `📬 Order #${orderId} has been delivered. Enjoy!`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `📬 Order for ${customerLegalName} was delivered. Order #${orderId}`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `📬 Order for ${customerLegalName} was delivered. Order #${orderId}`,
            },
        }
        const notificationSecondaryLanguageMessage = {
            [NOTIFICATION.TYPE.ORDER_FOR_APPROVAL]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `📤 تم إرسال طلبك رقم ${orderId} للموافقة.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `📤 ${customerLegalName} ارسال طلبك رقم ${orderId} للموافقة.`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `📤 ${customerLegalName} ارسال طلبك رقم ${orderId} للموافقة.`,
            },

            [NOTIFICATION.TYPE.ORDER_APPROVED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `✅ تمّت الموافقة على الطلب رقم ${orderId}!`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `✅ تمّت الموافقة على طلب من ${customerLegalName}. رقم الطلب ${orderId}`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `✅ تمّت الموافقة على طلب من ${customerLegalName}. رقم الطلب ${orderId}`,
            },

            [NOTIFICATION.TYPE.ORDER_RECEIVED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `📦 تم استلام طلبك رقم ${orderId}. شكرًا!`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `🆕 تم إنشاء طلب جديد لـ ${customerLegalName} بنجاح`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `🆕 تم إنشاء طلب جديد لـ ${customerLegalName} بنجاح`,
            },

            [NOTIFICATION.TYPE.ORDER_PREPARING]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `🔧 نقوم بتحضير طلبك رقم ${orderId}.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `🔧 جاري تحضير طلب لـ ${customerLegalName}. رقم الطلب ${orderId}`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `🔧 جاري تحضير طلب لـ ${customerLegalName}. رقم الطلب ${orderId}`,
            },

            [NOTIFICATION.TYPE.ORDER_SHIPPED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `🚚 طلبك رقم ${orderId} في الطريق إليك!`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `🚚 تم شحن طلب لـ ${customerLegalName}. رقم الطلب ${orderId}`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `🚚 تم شحن طلب لـ ${customerLegalName}. رقم الطلب ${orderId}`,
            },

            [NOTIFICATION.TYPE.ORDER_DELIVERED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `📬 تم توصيل طلبك رقم ${orderId}. نتمنى لك استخدامًا ممتعًا!`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `📬 تم توصيل طلب لـ ${customerLegalName}. رقم الطلب ${orderId}`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `📬 تم توصيل طلب لـ ${customerLegalName}. رقم الطلب ${orderId}`,
            },
        }
        const notificationCenterMessage = {
            [NOTIFICATION.TYPE.ORDER_FOR_APPROVAL]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `Your Order #${orderId} is <bold>sent for approval.</bold>`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `Order from <bold>${customerLegalName}</bold> was sent for approval. Order <bold>#${orderId}</bold>`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `Order from <bold>${customerLegalName}</bold> was sent for approval. Order <bold>#${orderId}</bold>`,
            },

            [NOTIFICATION.TYPE.ORDER_APPROVED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `Your Order #${orderId} was <bold>approved.</bold>`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `Order from ${customerLegalName} was approved. Order <bold>#${orderId}</bold>`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `Order from ${customerLegalName} was approved. Order <bold>#${orderId}</bold>`,
            },

            [NOTIFICATION.TYPE.ORDER_RECEIVED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `Your Order <bold>#${orderId}</bold> was sent successfully.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `A new order has been placed for <bold>${customerLegalName}</bold>`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `<bold>New order</bold> ${orderId}
<bold>Salesperson:</bold> ${salesPersonName}
<bold>Customer:</bold> ${customerLegalName}
<bold>Value:</bold> ${amount} ${currency}`,
            },

            [NOTIFICATION.TYPE.ORDER_PREPARING]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `Order <bold>#${orderId}</bold>: Currently being prepared.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `Order <bold>#${orderId}</bold> for <bold>${customerLegalName}</bold> is being prepared`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `<bold>Order</bold> ${orderId} is under processing.
<bold>Salesperson:</bold> ${salesPersonName}
<bold>Customer:</bold> ${customerLegalName}
<bold>Value:</bold> ${amount} ${currency}`,
            },

            [NOTIFICATION.TYPE.ORDER_SHIPPED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `Order #${orderId}: Shipped and on the way.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `Order <bold>#${orderId}</bold> for <bold>${customerLegalName}</bold> has been shipped.`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `<bold>Order</bold> ${orderId} has been shipped.
<bold>Salesperson:</bold> ${salesPersonName}
<bold>Customer:</bold> ${customerLegalName}
<bold>Value:</bold> ${amount} ${currency}`,
            },

            [NOTIFICATION.TYPE.ORDER_DELIVERED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `Order <bold>#${orderId}:</bold> Delivered successfully.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `Order <bold>#${orderId}</bold> for <bold>${customerLegalName}</bold> was delivered`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `"<bold>Order</bold> ${orderId} has been delivered
<bold>Salesperson:</bold> ${salesPersonName}
<bold>Customer:</bold> ${customerLegalName}
<bold>Value:</bold> ${amount} ${currency}"`,
            },
        }
        const notificationCenterSecondaryLanguageMessage = {
            [NOTIFICATION.TYPE.ORDER_FOR_APPROVAL]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `الطلب رقم <bold>${orderId}:</bold> تم إرساله للموافقة.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `تم إرسال الطلب من <bold>${customerLegalName}</bold> للموافقة.  رقم <bold>${orderId}</bold>`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `تم إرسال الطلب من <bold>${customerLegalName}</bold> للموافقة.  رقم <bold>${orderId}</bold>`,
            },

            [NOTIFICATION.TYPE.ORDER_APPROVED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `الطلب رقم <bold>${orderId}:</bold> تمت الموافقة عليه!`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `تمّت الموافقة على طلب من <bold>${customerLegalName}.</bold> رقم الطلب <bold>${orderId}</bold>`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `تمّت الموافقة على طلب من <bold>${customerLegalName}.</bold> رقم الطلب </bold>${orderId}</bold>`,
            },

            [NOTIFICATION.TYPE.ORDER_RECEIVED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `الطلب رقم <bold>${orderId}:</bold> تم الارسال بنجاح.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `تم إنشاء طلب جديد لـ <bold>${customerLegalName}</bold>`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `<bold>طلب جديد</bold> رقم ${orderId}
<bold>المندوب:</bold> ${salesPersonName}
<bold>العميل:</bold> ${customerLegalName}
<bold>القيمة:</bold> ${amount} ${currency}`,
            },

            [NOTIFICATION.TYPE.ORDER_PREPARING]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `الطلب رقم <bold>${orderId}:</bold> جارٍ التحضير.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `الطلب رقم <bold>${orderId}</bold> لـ <bold>${customerLegalName}</bold> قيد التحضير`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `<bold>طلب </bold>رقم ${orderId} جاري التحضير
<bold>المندوب:</bold> ${salesPersonName}
<bold>العميل:</bold> ${customerLegalName}
<bold>القيمة:</bold> ${amount} ${currency}`,
            },

            [NOTIFICATION.TYPE.ORDER_SHIPPED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `الطلب رقم ${orderId}: تم شحنه وهو في الطريق.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `تم شحن الطلب رقم <bold>${orderId}</bold> لـ <bold>${customerLegalName}</bold>`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `"<bold>طلب </bold>رقم ${orderId} تم الشحن
<bold>المندوب:</bold> ${salesPersonName}
<bold>العميل:</bold> ${customerLegalName}
<bold>القيمة:</bold> ${amount} ${currency}`,
            },

            [NOTIFICATION.TYPE.ORDER_DELIVERED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: `الطلب رقم ${orderId}: تم توصيله بنجاح.`,
                [PRIMITIVE_ROLES.SALES_PERSON]: `الطلب رقم <bold>${orderId}</bold> لـ <bold>${customerLegalName}</bold> تم التوصيل`,
                [PRIMITIVE_ROLES.SUPERVISOR]: `الطلب رقم <bold>${orderId}</bold> لـ <bold>${customerLegalName}</bold> تم التوصيل`,
            },
        }
        
        return {
            title: notificationTitle[notificationType],
            secondaryLanguageTitle: notificationSecondaryLanguageTitle[notificationType],
            message: notificationMessage[notificationType][userRole],
            smsBody: smsMessages[notificationType][userRole],
            secondaryLanguageMessage: notificationSecondaryLanguageMessage[notificationType][userRole],
            centerMessage: notificationCenterMessage[notificationType][userRole],
            centerSecondaryLanguageMessage: notificationCenterSecondaryLanguageMessage[notificationType][userRole],
        }
    }

    getRewardCoinsNotificationTemplate = (
        notificationType,
        points
    ) => {
        const notificationMessage = {
            [NOTIFICATION.TYPE.REWARD_PAYMENT]: `💰 You earned ${points} coins from your payment!`,
            [NOTIFICATION.TYPE.REWARD_PURCHASE]: `🛒 You earned ${points} coins from your latest purchase!`,
            [NOTIFICATION.TYPE.REWARD_MEMBER_SCAN]: `📍 ${points} coins added from your visit with the salesperson.`,
            [NOTIFICATION.TYPE.REWARD_MILESTONE_ONE]: `🎯 You've reached Milestone 1! ${points} coins added!`,
            [NOTIFICATION.TYPE.REWARD_MILESTONE_TWO]: `🚀 Milestone 2 unlocked! You earned ${points} coins.`,
            [NOTIFICATION.TYPE.REWARD_MILESTONE_THREE]: `🏆 Milestone 3 complete! ${points} coins awarded.`,
        }

        const notificationSecondaryLanguageMessage = {
            [NOTIFICATION.TYPE.REWARD_PAYMENT]: `💰 حصلت على ${points} نقطة مقابل الدفع!`,
            [NOTIFICATION.TYPE.REWARD_PURCHASE]: `🛒 حصلت على ${points} نقطة مقابل الشراء!`,
            [NOTIFICATION.TYPE.REWARD_MEMBER_SCAN]: `📍 تمت إضافة ${points} نقطة لزيارتك مع مندوب المبيعات.`,
            [NOTIFICATION.TYPE.REWARD_MILESTONE_ONE]: `🎯 لقد وصلت إلى المرحلة 1! تمت إضافة ${points} نقطة!`,
            [NOTIFICATION.TYPE.REWARD_MILESTONE_TWO]: `🚀 تم تحقيق المرحلة 2! لقد حصلت على ${points} نقطة.`,
            [NOTIFICATION.TYPE.REWARD_MILESTONE_THREE]: `🏆 تمت المرحلة 3! تم منحك ${points} نقطة.`,
        }

        const notificationCenterMessage = {
            [NOTIFICATION.TYPE.REWARD_PAYMENT]: `${points} coins added for your recent payment.`,
            [NOTIFICATION.TYPE.REWARD_PURCHASE]: `${points} coins added for your latest purchase.`,
            [NOTIFICATION.TYPE.REWARD_MEMBER_SCAN]: `${points} coins added for your salesperson visit.`,
            [NOTIFICATION.TYPE.REWARD_MILESTONE_ONE]: `Milestone 1 reached. ${points} coins added to your balance.`,
            [NOTIFICATION.TYPE.REWARD_MILESTONE_TWO]: `Milestone 2 unlocked. ${points} coins added to your balance.`,
            [NOTIFICATION.TYPE.REWARD_MILESTONE_THREE]: `Milestone 3 achieved. ${points} coins added to your balance.`,
        }

        const notificationCenterSecondaryLanguageMessage = {
            [NOTIFICATION.TYPE.REWARD_PAYMENT]: `تمت إضافة ${points} نقطة مقابل دفعتك الأخيرة.`,
            [NOTIFICATION.TYPE.REWARD_PURCHASE]: `تمت إضافة ${points} نقطة مقابل طلبك الأخير.`,
            [NOTIFICATION.TYPE.REWARD_MEMBER_SCAN]: `تمت إضافة ${points} نقطة بسبب زيارتك مع مندوب المبيعات.`,
            [NOTIFICATION.TYPE.REWARD_MILESTONE_ONE]: `تم تحقيق المرحلة 1. تمت إضافة ${points} نقطة إلى رصيدك.`,
            [NOTIFICATION.TYPE.REWARD_MILESTONE_TWO]: `تم تحقيق المرحلة 2. تمت إضافة ${points} نقطة إلى رصيدك.`,
            [NOTIFICATION.TYPE.REWARD_MILESTONE_THREE]: `تم تحقيق المرحلة 3. تمت إضافة ${points} نقطة إلى رصيدك.`,
        }

        return {
            title: "🎉 You've Earned Coins!",
            secondaryLanguageTitle: "🎉 لقد حصلت على نقاط!",
            message: notificationMessage[notificationType],
            secondaryLanguageMessage: notificationSecondaryLanguageMessage[notificationType],
            centerMessage: notificationCenterMessage[notificationType],
            centerSecondaryLanguageMessage: notificationCenterSecondaryLanguageMessage[notificationType],
        }
    }

}
