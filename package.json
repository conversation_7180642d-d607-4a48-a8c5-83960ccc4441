{"name": "hawak-user-backend", "version": "1.0.0", "description": "user service", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "createAPIDoc": "node Doc/create.js", "updateAPIDoc": "node Doc/update-postman-json.js", "prepare": "husky", "commitlint": "commitlint --edit"}, "repository": {"type": "git", "url": "git+https://github.com/ShriduttPatel10/Hawak-user-backend.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/ShriduttPatel10/Hawak-user-backend/issues"}, "homepage": "https://github.com/ShriduttPatel10/Hawak-user-backend#readme", "dependencies": {"@sendgrid/mail": "^7.7.0", "amazon-cognito-identity-js": "^5.2.10", "aws-sdk": "^2.616.0", "axios": "^0.19.2", "bcryptjs": "^2.4.3", "bwip-js": "^4.2.0", "cors": "^2.8.5", "cron": "^1.8.2", "csvtojson": "^2.0.0", "dotenv": "^8.2.0", "ejs": "^3.1.8", "exceljs": "^4.3.0", "express": "^4.17.1", "express-validator": "^6.4.0", "firebase-admin": "^12.0.0", "jsonwebtoken": "^8.5.1", "jwk-to-pem": "^2.0.5", "lodash.difference": "^4.5.0", "lodash.differencewith": "^4.5.0", "lodash.isempty": "^4.4.0", "lodash.mapkeys": "^4.6.0", "lodash.snakecase": "^4.1.1", "moment": "^2.24.0", "moment-timezone": "^0.5.43", "mongoose": "^8.5.3", "morgan": "^1.10.0", "multer": "^1.4.2", "node-fetch": "^2.6.7", "randomstring": "^1.1.5", "sqs-consumer": "^5.8.0", "swagger-ui-express": "^4.5.0", "twilio": "^3.70.0", "winston": "^3.13.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@commitlint/cli": "^19.2.1", "@commitlint/config-conventional": "^19.1.0", "husky": "^9.0.11", "nodemon": "^2.0.20", "postman-to-openapi": "^2.6.0"}}