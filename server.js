// PARSE .ENV
require('dotenv').config();

// FOR SERVER
// CHECK WITH PROTOCOL TO USE
const http = require('http')

const express = require('express') // NODE FRAMEWORK
const bodyParser = require('body-parser') // TO PARSE POST REQUEST
const cors = require('cors') // ALLOW CROSS ORIGIN REQUESTS
const path = require('path');
const morgan = require("morgan")

const swaggerUI = require('swagger-ui-express');

const { VALUES } = require('./Configs/constants');

const swaggerDocument = require(`./Doc/${VALUES.ENVIRONMENT}/openapi.json`)


// ---------------------------    SERVER CONFIGS ----------------------------------
const port = process.env.PORT || 8000
const app = express();

require('./Configs/globals'); // GLOBAL SETTINGS FILES
require("./Configs/Logger")
require('./Configs/cron-job'); // CRONJOB
require('./SQS/InitializeAllReceivers') //AWS-SQS

const server = http.createServer(app)

// --------------------------  SWAGGER    -----------------------------------------
app.use("/api-docs", swaggerUI.serve, swaggerUI.setup(swaggerDocument));


// --------------------------  MORGAN LOGGER MIDDLEWARE  -----------------------------------------
try {
    const skipLog = function (req, res) {
        if (
            req.url.includes(".log") ||
            req.originalUrl === "/" ||
            [
                "/robots.txt",
                "/favicon.ico"
            ].includes(req.url)
        ) {
            return true
        }
        return false
    }

    const morganMiddleware = morgan(
        "Morgan => Req: :status :method :url | ResTime: :response-time ms | TotalResTime: :total-time ms",
        {
            stream: {
                // Configure Morgan to use our custom logger with the http severity
                write: (message) => logger.http(message.trim()),
            },
            skip: skipLog,
        }
    )
    app.use(morganMiddleware)
}
catch (err) {
    // Handle the error here.
    logger.error(err)
}


// ------------------------      GLOBAL MIDDLEWARES -------------------------
app.use(bodyParser.json()) // ALLOW APPLICATION JSON
app.use(bodyParser.urlencoded({ extended: false })) // ALLOW URL ENCODED PARSER
app.use(cors({
    exposedHeaders: "refreshed-access-token"
})) // ALLOWED ALL CROSS ORIGIN REQUESTS
// app.use((req, res, next) => {
//     res.setHeader("Access-Control-Expose-Headers", "refreshed-access-token")
//     next();
// })
app.use(express.static(__dirname + '/Assets')); // SERVE STATIC IMAGES FROM ASSETS FOLDER
app.use(express.static(__dirname + '/Logs')); // LOGGER LOG FOLDER


// ------------------------    RESPONSE HANDLER    -------------------
app.use((req, res, next) => {
    const ResponseHandler = require('./Configs/responseHandler')
    res.handler = new ResponseHandler(req, res);
    next()
})

// --------------------------    ROUTES    ------------------
const appRoutes = require('./Routes');
appRoutes(app)


// --------------------------    GLOBAL ERROR HANDLER    ------------------
app.use((err, req, res, next) => {
    if (res.headersSent) {
        return next(err)
    }

    if (res.handler?.serverError) {
        return res.handler.serverError(err)
    }
    else {
        const {
            message,
            statusCode,
        } = err

        return res
            .status(statusCode)
            .json({
                message: message,
                data: err
            })
    }
})

// --------------------------   EJS TEMPLATE   ------------------
app.use("/Assets/", express.static(__dirname + '/Assets/'));
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'Views'));

// --------------------------    START SERVER    ---------------------
server.listen(port, () => {
    const message = `Server started on port ${port} :)`

    console.log(message)
    logger.info(message)
})

if (!VALUES.IS_PROD_ENV) {
    setTimeout(() => {
        logger.info(`process_env_PM2_PROGRAMMATIC ${VALUES.IS_APP_RUNNING_ON_SERVER}\n`)
    }, 3000);
}
